"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth";
exports.ids = ["vendor-chunks/@auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/@auth/prisma-adapter/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@auth/prisma-adapter/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PrismaAdapter: () => (/* binding */ PrismaAdapter)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/**\n * <div style={{display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", padding: 16}}>\n *  Official <a href=\"https://www.prisma.io/docs\">Prisma</a> adapter for Auth.js / NextAuth.js.\n *  <a href=\"https://www.prisma.io/\">\n *   <img style={{display: \"block\"}} src=\"https://authjs.dev/img/adapters/prisma.svg\" width=\"38\" />\n *  </a>\n * </div>\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install @prisma/client @auth/prisma-adapter\n * npm install prisma --save-dev\n * ```\n *\n * @module @auth/prisma-adapter\n */ \nfunction PrismaAdapter(prisma) {\n    const p = prisma;\n    return {\n        // We need to let Prisma generate the ID because our default UUID is incompatible with MongoDB\n        createUser: ({ id, ...data })=>p.user.create(stripUndefined(data)),\n        getUser: (id)=>p.user.findUnique({\n                where: {\n                    id\n                }\n            }),\n        getUserByEmail: (email)=>p.user.findUnique({\n                where: {\n                    email\n                }\n            }),\n        async getUserByAccount (provider_providerAccountId) {\n            const account = await p.account.findUnique({\n                where: {\n                    provider_providerAccountId\n                },\n                include: {\n                    user: true\n                }\n            });\n            return account?.user ?? null;\n        },\n        updateUser: ({ id, ...data })=>p.user.update({\n                where: {\n                    id\n                },\n                ...stripUndefined(data)\n            }),\n        deleteUser: (id)=>p.user.delete({\n                where: {\n                    id\n                }\n            }),\n        linkAccount: (data)=>p.account.create({\n                data\n            }),\n        unlinkAccount: (provider_providerAccountId)=>p.account.delete({\n                where: {\n                    provider_providerAccountId\n                }\n            }),\n        async getSessionAndUser (sessionToken) {\n            const userAndSession = await p.session.findUnique({\n                where: {\n                    sessionToken\n                },\n                include: {\n                    user: true\n                }\n            });\n            if (!userAndSession) return null;\n            const { user, ...session } = userAndSession;\n            return {\n                user,\n                session\n            };\n        },\n        createSession: (data)=>p.session.create(stripUndefined(data)),\n        updateSession: (data)=>p.session.update({\n                where: {\n                    sessionToken: data.sessionToken\n                },\n                ...stripUndefined(data)\n            }),\n        deleteSession: (sessionToken)=>p.session.delete({\n                where: {\n                    sessionToken\n                }\n            }),\n        async createVerificationToken (data) {\n            const verificationToken = await p.verificationToken.create(stripUndefined(data));\n            if (\"id\" in verificationToken && verificationToken.id) delete verificationToken.id;\n            return verificationToken;\n        },\n        async useVerificationToken (identifier_token) {\n            try {\n                const verificationToken = await p.verificationToken.delete({\n                    where: {\n                        identifier_token\n                    }\n                });\n                if (\"id\" in verificationToken && verificationToken.id) delete verificationToken.id;\n                return verificationToken;\n            } catch (error) {\n                // If token already used/deleted, just return null\n                // https://www.prisma.io/docs/reference/api-reference/error-reference#p2025\n                if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_0__.Prisma.PrismaClientKnownRequestError && error.code === \"P2025\") return null;\n                throw error;\n            }\n        },\n        async getAccount (providerAccountId, provider) {\n            return p.account.findFirst({\n                where: {\n                    providerAccountId,\n                    provider\n                }\n            });\n        },\n        async createAuthenticator (data) {\n            return p.authenticator.create(stripUndefined(data));\n        },\n        async getAuthenticator (credentialID) {\n            return p.authenticator.findUnique({\n                where: {\n                    credentialID\n                }\n            });\n        },\n        async listAuthenticatorsByUserId (userId) {\n            return p.authenticator.findMany({\n                where: {\n                    userId\n                }\n            });\n        },\n        async updateAuthenticatorCounter (credentialID, counter) {\n            return p.authenticator.update({\n                where: {\n                    credentialID\n                },\n                data: {\n                    counter\n                }\n            });\n        }\n    };\n}\n/** @see https://www.prisma.io/docs/orm/prisma-client/special-fields-and-types/null-and-undefined */ function stripUndefined(obj) {\n    const data = {};\n    for(const key in obj)if (obj[key] !== undefined) data[key] = obj[key];\n    return {\n        data\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/prisma-adapter/index.js\n");

/***/ })

};
;