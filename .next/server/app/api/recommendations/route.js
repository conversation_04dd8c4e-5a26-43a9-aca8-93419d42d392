"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/recommendations/route";
exports.ids = ["app/api/recommendations/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_mac_Documents_AI_Development_marketplace_src_app_api_recommendations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/recommendations/route.ts */ \"(rsc)/./src/app/api/recommendations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/recommendations/route\",\n        pathname: \"/api/recommendations\",\n        filename: \"route\",\n        bundlePath: \"app/api/recommendations/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/api/recommendations/route.ts\",\n    nextConfigOutput,\n    userland: _Users_mac_Documents_AI_Development_marketplace_src_app_api_recommendations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/recommendations/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user?.hashedPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isCorrectPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.hashedPassword);\n                if (!isCorrectPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return user;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/login\"\n    },\n    debug: \"development\" === \"development\",\n    session: {\n        strategy: \"jwt\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    }\n};\nconst handler = (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/recommendations/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/recommendations/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n// GET /api/recommendations - Get personalized product recommendations\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        const { searchParams } = new URL(request.url);\n        const limitParam = searchParams.get(\"limit\");\n        const limit = limitParam ? parseInt(limitParam, 10) : 8;\n        // If user is logged in, provide personalized recommendations\n        if (session?.user) {\n            const userId = session.user.id;\n            // 1. Get user's recently viewed products (from database or localStorage)\n            // 2. Get user's purchase history\n            // 3. Get user's saved searches\n            // 4. Use this data to generate personalized recommendations\n            // Get user's recently viewed products (if stored in database)\n            const recentlyViewed = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findMany({\n                where: {\n                    views: {\n                        some: {\n                            userId: userId\n                        }\n                    }\n                },\n                orderBy: {\n                    views: {\n                        _count: \"desc\"\n                    }\n                },\n                take: 5,\n                include: {\n                    category: true\n                }\n            });\n            // Get user's purchase history\n            const purchaseHistory = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.order.findMany({\n                where: {\n                    buyerId: userId,\n                    status: {\n                        in: [\n                            \"DELIVERED\",\n                            \"SHIPPING\"\n                        ]\n                    }\n                },\n                include: {\n                    product: {\n                        include: {\n                            category: true\n                        }\n                    }\n                },\n                take: 5\n            });\n            // Get user's saved searches\n            const savedSearches = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.savedSearch.findMany({\n                where: {\n                    userId: userId\n                },\n                orderBy: {\n                    lastUsed: \"desc\"\n                },\n                take: 3\n            });\n            // Extract categories of interest\n            const categoryIds = new Set();\n            // Add categories from recently viewed products\n            recentlyViewed.forEach((product)=>{\n                categoryIds.add(product.categoryId);\n            });\n            // Add categories from purchase history\n            purchaseHistory.forEach((order)=>{\n                categoryIds.add(order.product.categoryId);\n            });\n            // Add categories from saved searches\n            savedSearches.forEach((search)=>{\n                const filters = search.filters;\n                if (filters.category && filters.category !== \"all\") {\n                    // Find category by name or slug\n                    // This is a simplification - in a real app, you'd need to map the category name to ID\n                    categoryIds.add(filters.category);\n                }\n            });\n            // Get recommendations based on user's interests\n            let recommendations = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findMany({\n                where: {\n                    OR: [\n                        // Products in categories of interest\n                        {\n                            categoryId: {\n                                in: Array.from(categoryIds)\n                            }\n                        },\n                        // Products from sellers the user has purchased from\n                        {\n                            sellerId: {\n                                in: purchaseHistory.map((order)=>order.product.sellerId)\n                            }\n                        }\n                    ],\n                    // Exclude products the user has already viewed or purchased\n                    AND: [\n                        {\n                            id: {\n                                notIn: [\n                                    ...recentlyViewed.map((product)=>product.id),\n                                    ...purchaseHistory.map((order)=>order.productId)\n                                ]\n                            }\n                        },\n                        {\n                            status: \"ACTIVE\"\n                        }\n                    ]\n                },\n                include: {\n                    category: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    },\n                    seller: {\n                        select: {\n                            id: true,\n                            name: true,\n                            image: true,\n                            rating: true,\n                            idVerificationStatus: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: limit\n            });\n            // If we don't have enough recommendations, add some popular products\n            if (recommendations.length < limit) {\n                const additionalCount = limit - recommendations.length;\n                const existingIds = recommendations.map((product)=>product.id);\n                const popularProducts = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findMany({\n                    where: {\n                        id: {\n                            notIn: existingIds\n                        },\n                        status: \"ACTIVE\"\n                    },\n                    include: {\n                        category: {\n                            select: {\n                                id: true,\n                                name: true\n                            }\n                        },\n                        seller: {\n                            select: {\n                                id: true,\n                                name: true,\n                                image: true,\n                                rating: true,\n                                idVerificationStatus: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        views: {\n                            _count: \"desc\"\n                        }\n                    },\n                    take: additionalCount\n                });\n                recommendations = [\n                    ...recommendations,\n                    ...popularProducts\n                ];\n            }\n            // Format the seller verification status to match the expected format in the frontend\n            const formattedRecommendations = recommendations.map((product)=>({\n                    ...product,\n                    seller: {\n                        ...product.seller,\n                        verificationStatus: mapVerificationStatus(product.seller.idVerificationStatus)\n                    }\n                }));\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(formattedRecommendations);\n        } else {\n            const popularProducts = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findMany({\n                where: {\n                    status: \"ACTIVE\"\n                },\n                include: {\n                    category: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    },\n                    seller: {\n                        select: {\n                            id: true,\n                            name: true,\n                            image: true,\n                            rating: true,\n                            idVerificationStatus: true\n                        }\n                    }\n                },\n                orderBy: [\n                    {\n                        views: {\n                            _count: \"desc\"\n                        }\n                    },\n                    {\n                        createdAt: \"desc\"\n                    }\n                ],\n                take: limit\n            });\n            // Format the seller verification status to match the expected format in the frontend\n            const formattedProducts = popularProducts.map((product)=>({\n                    ...product,\n                    seller: {\n                        ...product.seller,\n                        verificationStatus: mapVerificationStatus(product.seller.idVerificationStatus)\n                    }\n                }));\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(formattedProducts);\n        }\n    } catch (error) {\n        console.error(\"Error fetching recommendations:\", error);\n        return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](\"Internal Server Error\", {\n            status: 500\n        });\n    }\n}\n// Helper function to map verification status from database enum to frontend format\nfunction mapVerificationStatus(status) {\n    switch(status){\n        case \"VERIFIED\":\n            return \"VERIFIED\";\n        case \"REJECTED\":\n            return \"REJECTED\";\n        case \"PENDING\":\n        default:\n            return \"PENDING\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/recommendations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n// For backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBeUIsRUFBY0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBRXBFLDZCQUE2QjtBQUM3QixpRUFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcblxuLy8gRm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcbmV4cG9ydCBkZWZhdWx0IHByaXNtYTsiXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/cookie","vendor-chunks/@auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();