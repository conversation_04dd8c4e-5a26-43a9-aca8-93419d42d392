"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/recommendations/route";
exports.ids = ["app/api/recommendations/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_mac_Documents_AI_Development_marketplace_src_app_api_recommendations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/recommendations/route.ts */ \"(rsc)/./src/app/api/recommendations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/recommendations/route\",\n        pathname: \"/api/recommendations\",\n        filename: \"route\",\n        bundlePath: \"app/api/recommendations/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/api/recommendations/route.ts\",\n    nextConfigOutput,\n    userland: _Users_mac_Documents_AI_Development_marketplace_src_app_api_recommendations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/recommendations/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user?.hashedPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isCorrectPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.hashedPassword);\n                if (!isCorrectPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return user;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/login\"\n    },\n    debug: \"development\" === \"development\",\n    session: {\n        strategy: \"jwt\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    }\n};\nconst handler = (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/recommendations/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/recommendations/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n// GET /api/recommendations - Get personalized product recommendations\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        const { searchParams } = new URL(request.url);\n        const limitParam = searchParams.get(\"limit\");\n        const limit = limitParam ? parseInt(limitParam, 10) : 8;\n        // If user is logged in, provide personalized recommendations\n        if (session?.user) {\n            const userId = session.user.id;\n            // 1. Get user's recently viewed products (from database or localStorage)\n            // 2. Get user's purchase history\n            // 3. Get user's saved searches\n            // 4. Use this data to generate personalized recommendations\n            // Get user's recently viewed products (if stored in database)\n            const recentlyViewed = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findMany({\n                where: {\n                    views: {\n                        some: {\n                            userId: userId\n                        }\n                    }\n                },\n                orderBy: {\n                    viewCount: \"desc\"\n                },\n                take: 5,\n                include: {\n                    category: true\n                }\n            });\n            // Get user's purchase history\n            const purchaseHistory = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.order.findMany({\n                where: {\n                    buyerId: userId,\n                    status: {\n                        in: [\n                            \"DELIVERED\",\n                            \"SHIPPING\"\n                        ]\n                    }\n                },\n                include: {\n                    product: {\n                        include: {\n                            category: true\n                        }\n                    }\n                },\n                take: 5\n            });\n            // Get user's saved searches\n            const savedSearches = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.savedSearch.findMany({\n                where: {\n                    userId: userId\n                },\n                orderBy: {\n                    lastUsed: \"desc\"\n                },\n                take: 3\n            });\n            // Extract categories of interest\n            const categoryIds = new Set();\n            // Add categories from recently viewed products\n            recentlyViewed.forEach((product)=>{\n                categoryIds.add(product.categoryId);\n            });\n            // Add categories from purchase history\n            purchaseHistory.forEach((order)=>{\n                categoryIds.add(order.product.categoryId);\n            });\n            // Add categories from saved searches\n            savedSearches.forEach((search)=>{\n                const filters = search.filters;\n                if (filters.category && filters.category !== \"all\") {\n                    // Find category by name or slug\n                    // This is a simplification - in a real app, you'd need to map the category name to ID\n                    categoryIds.add(filters.category);\n                }\n            });\n            // Get recommendations based on user's interests\n            let recommendations = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findMany({\n                where: {\n                    OR: [\n                        // Products in categories of interest\n                        {\n                            categoryId: {\n                                in: Array.from(categoryIds)\n                            }\n                        },\n                        // Products from sellers the user has purchased from\n                        {\n                            sellerId: {\n                                in: purchaseHistory.map((order)=>order.product.sellerId)\n                            }\n                        }\n                    ],\n                    // Exclude products the user has already viewed or purchased\n                    AND: [\n                        {\n                            id: {\n                                notIn: [\n                                    ...recentlyViewed.map((product)=>product.id),\n                                    ...purchaseHistory.map((order)=>order.productId)\n                                ]\n                            }\n                        },\n                        {\n                            status: \"ACTIVE\"\n                        }\n                    ]\n                },\n                include: {\n                    category: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    },\n                    seller: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: limit\n            });\n            // If we don't have enough recommendations, add some popular products\n            if (recommendations.length < limit) {\n                const additionalCount = limit - recommendations.length;\n                const existingIds = recommendations.map((product)=>product.id);\n                const popularProducts = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findMany({\n                    where: {\n                        id: {\n                            notIn: existingIds\n                        },\n                        status: \"ACTIVE\"\n                    },\n                    include: {\n                        category: {\n                            select: {\n                                id: true,\n                                name: true\n                            }\n                        },\n                        seller: {\n                            select: {\n                                id: true,\n                                name: true,\n                                email: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        viewCount: \"desc\"\n                    },\n                    take: additionalCount\n                });\n                recommendations = [\n                    ...recommendations,\n                    ...popularProducts\n                ];\n            }\n            // Format the seller verification status to match the expected format in the frontend\n            const formattedRecommendations = recommendations.map((product)=>({\n                    ...product,\n                    seller: {\n                        ...product.seller,\n                        verificationStatus: \"PENDING\"\n                    }\n                }));\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(formattedRecommendations);\n        } else {\n            const popularProducts = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findMany({\n                where: {\n                    status: \"ACTIVE\"\n                },\n                include: {\n                    category: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    },\n                    seller: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true\n                        }\n                    }\n                },\n                orderBy: [\n                    {\n                        viewCount: \"desc\"\n                    },\n                    {\n                        createdAt: \"desc\"\n                    }\n                ],\n                take: limit\n            });\n            // Format the seller verification status to match the expected format in the frontend\n            const formattedProducts = popularProducts.map((product)=>({\n                    ...product,\n                    seller: {\n                        ...product.seller,\n                        verificationStatus: \"PENDING\"\n                    }\n                }));\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(formattedProducts);\n        }\n    } catch (error) {\n        console.error(\"Error fetching recommendations:\", error);\n        return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](\"Internal Server Error\", {\n            status: 500\n        });\n    }\n}\n// Helper function to map verification status from database enum to frontend format\nfunction mapVerificationStatus(status) {\n    switch(status){\n        case \"VERIFIED\":\n            return \"VERIFIED\";\n        case \"REJECTED\":\n            return \"REJECTED\";\n        case \"PENDING\":\n        default:\n            return \"PENDING\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9yZWNvbW1lbmRhdGlvbnMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTJDO0FBQ0U7QUFDb0I7QUFDM0I7QUFFdEMsc0VBQXNFO0FBQy9ELGVBQWVJLElBQUlDLE9BQWdCO0lBQ3hDLElBQUk7UUFDRixNQUFNQyxVQUFVLE1BQU1MLDJEQUFnQkEsQ0FBQ0MscUVBQVdBO1FBQ2xELE1BQU0sRUFBRUssWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSUgsUUFBUUksR0FBRztRQUM1QyxNQUFNQyxhQUFhSCxhQUFhSSxHQUFHLENBQUM7UUFDcEMsTUFBTUMsUUFBUUYsYUFBYUcsU0FBU0gsWUFBWSxNQUFNO1FBRXRELDZEQUE2RDtRQUM3RCxJQUFJSixTQUFTUSxNQUFNO1lBQ2pCLE1BQU1DLFNBQVNULFFBQVFRLElBQUksQ0FBQ0UsRUFBRTtZQUU5Qix5RUFBeUU7WUFDekUsaUNBQWlDO1lBQ2pDLCtCQUErQjtZQUMvQiw0REFBNEQ7WUFFNUQsOERBQThEO1lBQzlELE1BQU1DLGlCQUFpQixNQUFNZCwrQ0FBTUEsQ0FBQ2UsT0FBTyxDQUFDQyxRQUFRLENBQUM7Z0JBQ25EQyxPQUFPO29CQUNMQyxPQUFPO3dCQUNMQyxNQUFNOzRCQUNKUCxRQUFRQTt3QkFDVjtvQkFDRjtnQkFDRjtnQkFDQVEsU0FBUztvQkFDUEMsV0FBVztnQkFDYjtnQkFDQUMsTUFBTTtnQkFDTkMsU0FBUztvQkFDUEMsVUFBVTtnQkFDWjtZQUNGO1lBRUEsOEJBQThCO1lBQzlCLE1BQU1DLGtCQUFrQixNQUFNekIsK0NBQU1BLENBQUMwQixLQUFLLENBQUNWLFFBQVEsQ0FBQztnQkFDbERDLE9BQU87b0JBQ0xVLFNBQVNmO29CQUNUZ0IsUUFBUTt3QkFDTkMsSUFBSTs0QkFBQzs0QkFBYTt5QkFBVztvQkFDL0I7Z0JBQ0Y7Z0JBQ0FOLFNBQVM7b0JBQ1BSLFNBQVM7d0JBQ1BRLFNBQVM7NEJBQ1BDLFVBQVU7d0JBQ1o7b0JBQ0Y7Z0JBQ0Y7Z0JBQ0FGLE1BQU07WUFDUjtZQUVBLDRCQUE0QjtZQUM1QixNQUFNUSxnQkFBZ0IsTUFBTTlCLCtDQUFNQSxDQUFDK0IsV0FBVyxDQUFDZixRQUFRLENBQUM7Z0JBQ3REQyxPQUFPO29CQUNMTCxRQUFRQTtnQkFDVjtnQkFDQVEsU0FBUztvQkFDUFksVUFBVTtnQkFDWjtnQkFDQVYsTUFBTTtZQUNSO1lBRUEsaUNBQWlDO1lBQ2pDLE1BQU1XLGNBQWMsSUFBSUM7WUFFeEIsK0NBQStDO1lBQy9DcEIsZUFBZXFCLE9BQU8sQ0FBQ3BCLENBQUFBO2dCQUNyQmtCLFlBQVlHLEdBQUcsQ0FBQ3JCLFFBQVFzQixVQUFVO1lBQ3BDO1lBRUEsdUNBQXVDO1lBQ3ZDWixnQkFBZ0JVLE9BQU8sQ0FBQ1QsQ0FBQUE7Z0JBQ3RCTyxZQUFZRyxHQUFHLENBQUNWLE1BQU1YLE9BQU8sQ0FBQ3NCLFVBQVU7WUFDMUM7WUFFQSxxQ0FBcUM7WUFDckNQLGNBQWNLLE9BQU8sQ0FBQ0csQ0FBQUE7Z0JBQ3BCLE1BQU1DLFVBQVVELE9BQU9DLE9BQU87Z0JBQzlCLElBQUlBLFFBQVFmLFFBQVEsSUFBSWUsUUFBUWYsUUFBUSxLQUFLLE9BQU87b0JBQ2xELGdDQUFnQztvQkFDaEMsc0ZBQXNGO29CQUN0RlMsWUFBWUcsR0FBRyxDQUFDRyxRQUFRZixRQUFRO2dCQUNsQztZQUNGO1lBRUEsZ0RBQWdEO1lBQ2hELElBQUlnQixrQkFBa0IsTUFBTXhDLCtDQUFNQSxDQUFDZSxPQUFPLENBQUNDLFFBQVEsQ0FBQztnQkFDbERDLE9BQU87b0JBQ0x3QixJQUFJO3dCQUNGLHFDQUFxQzt3QkFDckM7NEJBQ0VKLFlBQVk7Z0NBQ1ZSLElBQUlhLE1BQU1DLElBQUksQ0FBQ1Y7NEJBQ2pCO3dCQUNGO3dCQUNBLG9EQUFvRDt3QkFDcEQ7NEJBQ0VXLFVBQVU7Z0NBQ1JmLElBQUlKLGdCQUFnQm9CLEdBQUcsQ0FBQ25CLENBQUFBLFFBQVNBLE1BQU1YLE9BQU8sQ0FBQzZCLFFBQVE7NEJBQ3pEO3dCQUNGO3FCQUNEO29CQUNELDREQUE0RDtvQkFDNURFLEtBQUs7d0JBQ0g7NEJBQ0VqQyxJQUFJO2dDQUNGa0MsT0FBTzt1Q0FDRmpDLGVBQWUrQixHQUFHLENBQUM5QixDQUFBQSxVQUFXQSxRQUFRRixFQUFFO3VDQUN4Q1ksZ0JBQWdCb0IsR0FBRyxDQUFDbkIsQ0FBQUEsUUFBU0EsTUFBTXNCLFNBQVM7aUNBQ2hEOzRCQUNIO3dCQUNGO3dCQUNBOzRCQUNFcEIsUUFBUTt3QkFDVjtxQkFDRDtnQkFDSDtnQkFDQUwsU0FBUztvQkFDUEMsVUFBVTt3QkFDUnlCLFFBQVE7NEJBQ05wQyxJQUFJOzRCQUNKcUMsTUFBTTt3QkFDUjtvQkFDRjtvQkFDQUMsUUFBUTt3QkFDTkYsUUFBUTs0QkFDTnBDLElBQUk7NEJBQ0pxQyxNQUFNOzRCQUNORSxPQUFPO3dCQUNUO29CQUNGO2dCQUNGO2dCQUNBaEMsU0FBUztvQkFDUGlDLFdBQVc7Z0JBQ2I7Z0JBQ0EvQixNQUFNYjtZQUNSO1lBRUEscUVBQXFFO1lBQ3JFLElBQUkrQixnQkFBZ0JjLE1BQU0sR0FBRzdDLE9BQU87Z0JBQ2xDLE1BQU04QyxrQkFBa0I5QyxRQUFRK0IsZ0JBQWdCYyxNQUFNO2dCQUN0RCxNQUFNRSxjQUFjaEIsZ0JBQWdCSyxHQUFHLENBQUM5QixDQUFBQSxVQUFXQSxRQUFRRixFQUFFO2dCQUU3RCxNQUFNNEMsa0JBQWtCLE1BQU16RCwrQ0FBTUEsQ0FBQ2UsT0FBTyxDQUFDQyxRQUFRLENBQUM7b0JBQ3BEQyxPQUFPO3dCQUNMSixJQUFJOzRCQUNGa0MsT0FBT1M7d0JBQ1Q7d0JBQ0E1QixRQUFRO29CQUNWO29CQUNBTCxTQUFTO3dCQUNQQyxVQUFVOzRCQUNSeUIsUUFBUTtnQ0FDTnBDLElBQUk7Z0NBQ0pxQyxNQUFNOzRCQUNSO3dCQUNGO3dCQUNBQyxRQUFROzRCQUNORixRQUFRO2dDQUNOcEMsSUFBSTtnQ0FDSnFDLE1BQU07Z0NBQ05FLE9BQU87NEJBQ1Q7d0JBQ0Y7b0JBQ0Y7b0JBQ0FoQyxTQUFTO3dCQUNQQyxXQUFXO29CQUNiO29CQUNBQyxNQUFNaUM7Z0JBQ1I7Z0JBRUFmLGtCQUFrQjt1QkFBSUE7dUJBQW9CaUI7aUJBQWdCO1lBQzVEO1lBRUEscUZBQXFGO1lBQ3JGLE1BQU1DLDJCQUEyQmxCLGdCQUFnQkssR0FBRyxDQUFDOUIsQ0FBQUEsVUFBWTtvQkFDL0QsR0FBR0EsT0FBTztvQkFDVm9DLFFBQVE7d0JBQ04sR0FBR3BDLFFBQVFvQyxNQUFNO3dCQUNqQlEsb0JBQW9CO29CQUN0QjtnQkFDRjtZQUVBLE9BQU85RCxrRkFBWUEsQ0FBQytELElBQUksQ0FBQ0Y7UUFDM0IsT0FFSztZQUNILE1BQU1ELGtCQUFrQixNQUFNekQsK0NBQU1BLENBQUNlLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDO2dCQUNwREMsT0FBTztvQkFDTFcsUUFBUTtnQkFDVjtnQkFDQUwsU0FBUztvQkFDUEMsVUFBVTt3QkFDUnlCLFFBQVE7NEJBQ05wQyxJQUFJOzRCQUNKcUMsTUFBTTt3QkFDUjtvQkFDRjtvQkFDQUMsUUFBUTt3QkFDTkYsUUFBUTs0QkFDTnBDLElBQUk7NEJBQ0pxQyxNQUFNOzRCQUNORSxPQUFPO3dCQUNUO29CQUNGO2dCQUNGO2dCQUNBaEMsU0FBUztvQkFDUDt3QkFDRUMsV0FBVztvQkFDYjtvQkFDQTt3QkFDRWdDLFdBQVc7b0JBQ2I7aUJBQ0Q7Z0JBQ0QvQixNQUFNYjtZQUNSO1lBRUEscUZBQXFGO1lBQ3JGLE1BQU1vRCxvQkFBb0JKLGdCQUFnQlosR0FBRyxDQUFDOUIsQ0FBQUEsVUFBWTtvQkFDeEQsR0FBR0EsT0FBTztvQkFDVm9DLFFBQVE7d0JBQ04sR0FBR3BDLFFBQVFvQyxNQUFNO3dCQUNqQlEsb0JBQW9CO29CQUN0QjtnQkFDRjtZQUVBLE9BQU85RCxrRkFBWUEsQ0FBQytELElBQUksQ0FBQ0M7UUFDM0I7SUFDRixFQUFFLE9BQU9DLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7UUFDakQsT0FBTyxJQUFJakUsa0ZBQVlBLENBQUMseUJBQXlCO1lBQUUrQixRQUFRO1FBQUk7SUFDakU7QUFDRjtBQUVBLG1GQUFtRjtBQUNuRixTQUFTb0Msc0JBQXNCcEMsTUFBVztJQUN4QyxPQUFRQTtRQUNOLEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1FBQ0w7WUFDRSxPQUFPO0lBQ1g7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vc3JjL2FwcC9hcGkvcmVjb21tZW5kYXRpb25zL3JvdXRlLnRzP2JkYWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgZ2V0U2VydmVyU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aCc7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUnO1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnQC9saWIvcHJpc21hJztcblxuLy8gR0VUIC9hcGkvcmVjb21tZW5kYXRpb25zIC0gR2V0IHBlcnNvbmFsaXplZCBwcm9kdWN0IHJlY29tbWVuZGF0aW9uc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXF1ZXN0OiBSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpO1xuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcbiAgICBjb25zdCBsaW1pdFBhcmFtID0gc2VhcmNoUGFyYW1zLmdldCgnbGltaXQnKTtcbiAgICBjb25zdCBsaW1pdCA9IGxpbWl0UGFyYW0gPyBwYXJzZUludChsaW1pdFBhcmFtLCAxMCkgOiA4O1xuXG4gICAgLy8gSWYgdXNlciBpcyBsb2dnZWQgaW4sIHByb3ZpZGUgcGVyc29uYWxpemVkIHJlY29tbWVuZGF0aW9uc1xuICAgIGlmIChzZXNzaW9uPy51c2VyKSB7XG4gICAgICBjb25zdCB1c2VySWQgPSBzZXNzaW9uLnVzZXIuaWQ7XG5cbiAgICAgIC8vIDEuIEdldCB1c2VyJ3MgcmVjZW50bHkgdmlld2VkIHByb2R1Y3RzIChmcm9tIGRhdGFiYXNlIG9yIGxvY2FsU3RvcmFnZSlcbiAgICAgIC8vIDIuIEdldCB1c2VyJ3MgcHVyY2hhc2UgaGlzdG9yeVxuICAgICAgLy8gMy4gR2V0IHVzZXIncyBzYXZlZCBzZWFyY2hlc1xuICAgICAgLy8gNC4gVXNlIHRoaXMgZGF0YSB0byBnZW5lcmF0ZSBwZXJzb25hbGl6ZWQgcmVjb21tZW5kYXRpb25zXG5cbiAgICAgIC8vIEdldCB1c2VyJ3MgcmVjZW50bHkgdmlld2VkIHByb2R1Y3RzIChpZiBzdG9yZWQgaW4gZGF0YWJhc2UpXG4gICAgICBjb25zdCByZWNlbnRseVZpZXdlZCA9IGF3YWl0IHByaXNtYS5wcm9kdWN0LmZpbmRNYW55KHtcbiAgICAgICAgd2hlcmU6IHtcbiAgICAgICAgICB2aWV3czoge1xuICAgICAgICAgICAgc29tZToge1xuICAgICAgICAgICAgICB1c2VySWQ6IHVzZXJJZCxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgb3JkZXJCeToge1xuICAgICAgICAgIHZpZXdDb3VudDogJ2Rlc2MnLFxuICAgICAgICB9LFxuICAgICAgICB0YWtlOiA1LFxuICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgY2F0ZWdvcnk6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgLy8gR2V0IHVzZXIncyBwdXJjaGFzZSBoaXN0b3J5XG4gICAgICBjb25zdCBwdXJjaGFzZUhpc3RvcnkgPSBhd2FpdCBwcmlzbWEub3JkZXIuZmluZE1hbnkoe1xuICAgICAgICB3aGVyZToge1xuICAgICAgICAgIGJ1eWVySWQ6IHVzZXJJZCxcbiAgICAgICAgICBzdGF0dXM6IHtcbiAgICAgICAgICAgIGluOiBbJ0RFTElWRVJFRCcsICdTSElQUElORyddLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICBwcm9kdWN0OiB7XG4gICAgICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgICAgIGNhdGVnb3J5OiB0cnVlLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgICB0YWtlOiA1LFxuICAgICAgfSk7XG5cbiAgICAgIC8vIEdldCB1c2VyJ3Mgc2F2ZWQgc2VhcmNoZXNcbiAgICAgIGNvbnN0IHNhdmVkU2VhcmNoZXMgPSBhd2FpdCBwcmlzbWEuc2F2ZWRTZWFyY2guZmluZE1hbnkoe1xuICAgICAgICB3aGVyZToge1xuICAgICAgICAgIHVzZXJJZDogdXNlcklkLFxuICAgICAgICB9LFxuICAgICAgICBvcmRlckJ5OiB7XG4gICAgICAgICAgbGFzdFVzZWQ6ICdkZXNjJyxcbiAgICAgICAgfSxcbiAgICAgICAgdGFrZTogMyxcbiAgICAgIH0pO1xuXG4gICAgICAvLyBFeHRyYWN0IGNhdGVnb3JpZXMgb2YgaW50ZXJlc3RcbiAgICAgIGNvbnN0IGNhdGVnb3J5SWRzID0gbmV3IFNldDxzdHJpbmc+KCk7XG5cbiAgICAgIC8vIEFkZCBjYXRlZ29yaWVzIGZyb20gcmVjZW50bHkgdmlld2VkIHByb2R1Y3RzXG4gICAgICByZWNlbnRseVZpZXdlZC5mb3JFYWNoKHByb2R1Y3QgPT4ge1xuICAgICAgICBjYXRlZ29yeUlkcy5hZGQocHJvZHVjdC5jYXRlZ29yeUlkKTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBBZGQgY2F0ZWdvcmllcyBmcm9tIHB1cmNoYXNlIGhpc3RvcnlcbiAgICAgIHB1cmNoYXNlSGlzdG9yeS5mb3JFYWNoKG9yZGVyID0+IHtcbiAgICAgICAgY2F0ZWdvcnlJZHMuYWRkKG9yZGVyLnByb2R1Y3QuY2F0ZWdvcnlJZCk7XG4gICAgICB9KTtcblxuICAgICAgLy8gQWRkIGNhdGVnb3JpZXMgZnJvbSBzYXZlZCBzZWFyY2hlc1xuICAgICAgc2F2ZWRTZWFyY2hlcy5mb3JFYWNoKHNlYXJjaCA9PiB7XG4gICAgICAgIGNvbnN0IGZpbHRlcnMgPSBzZWFyY2guZmlsdGVycyBhcyBhbnk7XG4gICAgICAgIGlmIChmaWx0ZXJzLmNhdGVnb3J5ICYmIGZpbHRlcnMuY2F0ZWdvcnkgIT09ICdhbGwnKSB7XG4gICAgICAgICAgLy8gRmluZCBjYXRlZ29yeSBieSBuYW1lIG9yIHNsdWdcbiAgICAgICAgICAvLyBUaGlzIGlzIGEgc2ltcGxpZmljYXRpb24gLSBpbiBhIHJlYWwgYXBwLCB5b3UnZCBuZWVkIHRvIG1hcCB0aGUgY2F0ZWdvcnkgbmFtZSB0byBJRFxuICAgICAgICAgIGNhdGVnb3J5SWRzLmFkZChmaWx0ZXJzLmNhdGVnb3J5KTtcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIC8vIEdldCByZWNvbW1lbmRhdGlvbnMgYmFzZWQgb24gdXNlcidzIGludGVyZXN0c1xuICAgICAgbGV0IHJlY29tbWVuZGF0aW9ucyA9IGF3YWl0IHByaXNtYS5wcm9kdWN0LmZpbmRNYW55KHtcbiAgICAgICAgd2hlcmU6IHtcbiAgICAgICAgICBPUjogW1xuICAgICAgICAgICAgLy8gUHJvZHVjdHMgaW4gY2F0ZWdvcmllcyBvZiBpbnRlcmVzdFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICBjYXRlZ29yeUlkOiB7XG4gICAgICAgICAgICAgICAgaW46IEFycmF5LmZyb20oY2F0ZWdvcnlJZHMpLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIC8vIFByb2R1Y3RzIGZyb20gc2VsbGVycyB0aGUgdXNlciBoYXMgcHVyY2hhc2VkIGZyb21cbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgc2VsbGVySWQ6IHtcbiAgICAgICAgICAgICAgICBpbjogcHVyY2hhc2VIaXN0b3J5Lm1hcChvcmRlciA9PiBvcmRlci5wcm9kdWN0LnNlbGxlcklkKSxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgXSxcbiAgICAgICAgICAvLyBFeGNsdWRlIHByb2R1Y3RzIHRoZSB1c2VyIGhhcyBhbHJlYWR5IHZpZXdlZCBvciBwdXJjaGFzZWRcbiAgICAgICAgICBBTkQ6IFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgaWQ6IHtcbiAgICAgICAgICAgICAgICBub3RJbjogW1xuICAgICAgICAgICAgICAgICAgLi4ucmVjZW50bHlWaWV3ZWQubWFwKHByb2R1Y3QgPT4gcHJvZHVjdC5pZCksXG4gICAgICAgICAgICAgICAgICAuLi5wdXJjaGFzZUhpc3RvcnkubWFwKG9yZGVyID0+IG9yZGVyLnByb2R1Y3RJZCksXG4gICAgICAgICAgICAgICAgXSxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIHN0YXR1czogJ0FDVElWRScsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgIF0sXG4gICAgICAgIH0sXG4gICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICBjYXRlZ29yeToge1xuICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgIGlkOiB0cnVlLFxuICAgICAgICAgICAgICBuYW1lOiB0cnVlLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHNlbGxlcjoge1xuICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgIGlkOiB0cnVlLFxuICAgICAgICAgICAgICBuYW1lOiB0cnVlLFxuICAgICAgICAgICAgICBlbWFpbDogdHJ1ZSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgb3JkZXJCeToge1xuICAgICAgICAgIGNyZWF0ZWRBdDogJ2Rlc2MnLFxuICAgICAgICB9LFxuICAgICAgICB0YWtlOiBsaW1pdCxcbiAgICAgIH0pO1xuXG4gICAgICAvLyBJZiB3ZSBkb24ndCBoYXZlIGVub3VnaCByZWNvbW1lbmRhdGlvbnMsIGFkZCBzb21lIHBvcHVsYXIgcHJvZHVjdHNcbiAgICAgIGlmIChyZWNvbW1lbmRhdGlvbnMubGVuZ3RoIDwgbGltaXQpIHtcbiAgICAgICAgY29uc3QgYWRkaXRpb25hbENvdW50ID0gbGltaXQgLSByZWNvbW1lbmRhdGlvbnMubGVuZ3RoO1xuICAgICAgICBjb25zdCBleGlzdGluZ0lkcyA9IHJlY29tbWVuZGF0aW9ucy5tYXAocHJvZHVjdCA9PiBwcm9kdWN0LmlkKTtcblxuICAgICAgICBjb25zdCBwb3B1bGFyUHJvZHVjdHMgPSBhd2FpdCBwcmlzbWEucHJvZHVjdC5maW5kTWFueSh7XG4gICAgICAgICAgd2hlcmU6IHtcbiAgICAgICAgICAgIGlkOiB7XG4gICAgICAgICAgICAgIG5vdEluOiBleGlzdGluZ0lkcyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzdGF0dXM6ICdBQ1RJVkUnLFxuICAgICAgICAgIH0sXG4gICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgY2F0ZWdvcnk6IHtcbiAgICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgbmFtZTogdHJ1ZSxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzZWxsZXI6IHtcbiAgICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgbmFtZTogdHJ1ZSxcbiAgICAgICAgICAgICAgICBlbWFpbDogdHJ1ZSxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgICBvcmRlckJ5OiB7XG4gICAgICAgICAgICB2aWV3Q291bnQ6ICdkZXNjJyxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHRha2U6IGFkZGl0aW9uYWxDb3VudCxcbiAgICAgICAgfSk7XG5cbiAgICAgICAgcmVjb21tZW5kYXRpb25zID0gWy4uLnJlY29tbWVuZGF0aW9ucywgLi4ucG9wdWxhclByb2R1Y3RzXTtcbiAgICAgIH1cblxuICAgICAgLy8gRm9ybWF0IHRoZSBzZWxsZXIgdmVyaWZpY2F0aW9uIHN0YXR1cyB0byBtYXRjaCB0aGUgZXhwZWN0ZWQgZm9ybWF0IGluIHRoZSBmcm9udGVuZFxuICAgICAgY29uc3QgZm9ybWF0dGVkUmVjb21tZW5kYXRpb25zID0gcmVjb21tZW5kYXRpb25zLm1hcChwcm9kdWN0ID0+ICh7XG4gICAgICAgIC4uLnByb2R1Y3QsXG4gICAgICAgIHNlbGxlcjoge1xuICAgICAgICAgIC4uLnByb2R1Y3Quc2VsbGVyLFxuICAgICAgICAgIHZlcmlmaWNhdGlvblN0YXR1czogJ1BFTkRJTkcnIGFzIGNvbnN0LFxuICAgICAgICB9LFxuICAgICAgfSkpO1xuXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oZm9ybWF0dGVkUmVjb21tZW5kYXRpb25zKTtcbiAgICB9XG4gICAgLy8gRm9yIG5vbi1sb2dnZWQgaW4gdXNlcnMsIHByb3ZpZGUgcG9wdWxhciBwcm9kdWN0c1xuICAgIGVsc2Uge1xuICAgICAgY29uc3QgcG9wdWxhclByb2R1Y3RzID0gYXdhaXQgcHJpc21hLnByb2R1Y3QuZmluZE1hbnkoe1xuICAgICAgICB3aGVyZToge1xuICAgICAgICAgIHN0YXR1czogJ0FDVElWRScsXG4gICAgICAgIH0sXG4gICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICBjYXRlZ29yeToge1xuICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgIGlkOiB0cnVlLFxuICAgICAgICAgICAgICBuYW1lOiB0cnVlLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHNlbGxlcjoge1xuICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgIGlkOiB0cnVlLFxuICAgICAgICAgICAgICBuYW1lOiB0cnVlLFxuICAgICAgICAgICAgICBlbWFpbDogdHJ1ZSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgb3JkZXJCeTogW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIHZpZXdDb3VudDogJ2Rlc2MnLFxuICAgICAgICAgIH0sXG4gICAgICAgICAge1xuICAgICAgICAgICAgY3JlYXRlZEF0OiAnZGVzYycsXG4gICAgICAgICAgfSxcbiAgICAgICAgXSxcbiAgICAgICAgdGFrZTogbGltaXQsXG4gICAgICB9KTtcblxuICAgICAgLy8gRm9ybWF0IHRoZSBzZWxsZXIgdmVyaWZpY2F0aW9uIHN0YXR1cyB0byBtYXRjaCB0aGUgZXhwZWN0ZWQgZm9ybWF0IGluIHRoZSBmcm9udGVuZFxuICAgICAgY29uc3QgZm9ybWF0dGVkUHJvZHVjdHMgPSBwb3B1bGFyUHJvZHVjdHMubWFwKHByb2R1Y3QgPT4gKHtcbiAgICAgICAgLi4ucHJvZHVjdCxcbiAgICAgICAgc2VsbGVyOiB7XG4gICAgICAgICAgLi4ucHJvZHVjdC5zZWxsZXIsXG4gICAgICAgICAgdmVyaWZpY2F0aW9uU3RhdHVzOiAnUEVORElORycgYXMgY29uc3QsXG4gICAgICAgIH0sXG4gICAgICB9KSk7XG5cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihmb3JtYXR0ZWRQcm9kdWN0cyk7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHJlY29tbWVuZGF0aW9uczonLCBlcnJvcik7XG4gICAgcmV0dXJuIG5ldyBOZXh0UmVzcG9uc2UoJ0ludGVybmFsIFNlcnZlciBFcnJvcicsIHsgc3RhdHVzOiA1MDAgfSk7XG4gIH1cbn1cblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIG1hcCB2ZXJpZmljYXRpb24gc3RhdHVzIGZyb20gZGF0YWJhc2UgZW51bSB0byBmcm9udGVuZCBmb3JtYXRcbmZ1bmN0aW9uIG1hcFZlcmlmaWNhdGlvblN0YXR1cyhzdGF0dXM6IGFueSk6ICdQRU5ESU5HJyB8ICdWRVJJRklFRCcgfCAnUkVKRUNURUQnIHtcbiAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICBjYXNlICdWRVJJRklFRCc6XG4gICAgICByZXR1cm4gJ1ZFUklGSUVEJztcbiAgICBjYXNlICdSRUpFQ1RFRCc6XG4gICAgICByZXR1cm4gJ1JFSkVDVEVEJztcbiAgICBjYXNlICdQRU5ESU5HJzpcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuICdQRU5ESU5HJztcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImdldFNlcnZlclNlc3Npb24iLCJhdXRoT3B0aW9ucyIsInByaXNtYSIsIkdFVCIsInJlcXVlc3QiLCJzZXNzaW9uIiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwibGltaXRQYXJhbSIsImdldCIsImxpbWl0IiwicGFyc2VJbnQiLCJ1c2VyIiwidXNlcklkIiwiaWQiLCJyZWNlbnRseVZpZXdlZCIsInByb2R1Y3QiLCJmaW5kTWFueSIsIndoZXJlIiwidmlld3MiLCJzb21lIiwib3JkZXJCeSIsInZpZXdDb3VudCIsInRha2UiLCJpbmNsdWRlIiwiY2F0ZWdvcnkiLCJwdXJjaGFzZUhpc3RvcnkiLCJvcmRlciIsImJ1eWVySWQiLCJzdGF0dXMiLCJpbiIsInNhdmVkU2VhcmNoZXMiLCJzYXZlZFNlYXJjaCIsImxhc3RVc2VkIiwiY2F0ZWdvcnlJZHMiLCJTZXQiLCJmb3JFYWNoIiwiYWRkIiwiY2F0ZWdvcnlJZCIsInNlYXJjaCIsImZpbHRlcnMiLCJyZWNvbW1lbmRhdGlvbnMiLCJPUiIsIkFycmF5IiwiZnJvbSIsInNlbGxlcklkIiwibWFwIiwiQU5EIiwibm90SW4iLCJwcm9kdWN0SWQiLCJzZWxlY3QiLCJuYW1lIiwic2VsbGVyIiwiZW1haWwiLCJjcmVhdGVkQXQiLCJsZW5ndGgiLCJhZGRpdGlvbmFsQ291bnQiLCJleGlzdGluZ0lkcyIsInBvcHVsYXJQcm9kdWN0cyIsImZvcm1hdHRlZFJlY29tbWVuZGF0aW9ucyIsInZlcmlmaWNhdGlvblN0YXR1cyIsImpzb24iLCJmb3JtYXR0ZWRQcm9kdWN0cyIsImVycm9yIiwiY29uc29sZSIsIm1hcFZlcmlmaWNhdGlvblN0YXR1cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/recommendations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n// For backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBeUIsRUFBY0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBRXBFLDZCQUE2QjtBQUM3QixpRUFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcblxuLy8gRm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcbmV4cG9ydCBkZWZhdWx0IHByaXNtYTsiXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/cookie","vendor-chunks/@auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();