/*
  Warnings:

  - You are about to drop the column `createdAt` on the `CategoryAttribute` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `CategoryAttribute` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[categoryId,name]` on the table `CategoryAttribute` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "CategoryAttribute" DROP COLUMN "createdAt",
DROP COLUMN "updatedAt";

-- CreateIndex
CREATE UNIQUE INDEX "CategoryAttribute_categoryId_name_key" ON "CategoryAttribute"("categoryId", "name");
