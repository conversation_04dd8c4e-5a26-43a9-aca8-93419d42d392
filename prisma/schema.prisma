generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String              @id @default(cuid())
  name                 String?
  email                String              @unique
  hashedPassword       String?
  role                 Role                @default(USER)
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  products             Product[]
  orders               Order[]
  reviews              Review[]
  followers            Follow[]            @relation("Following")
  following            Follow[]            @relation("Followers")
  badges               Badge[]
  achievements         Achievement[]
  notifications        Notification[]
  accounts             Account[]
  sessions             Session[]
  seller               Seller?
  buyerChats           Chat[]              @relation("BuyerChats")
  sellerChats          Chat[]              @relation("SellerChats")
  messages             Message[]
  openedDisputes       Dispute[]           @relation("OpenedDisputes")
  assignedDisputes     Dispute[]           @relation("AssignedDisputes")
  disputeMessages      DisputeMessage[]
  disputeEvidences     DisputeEvidence[]
  riskScore            Int                 @default(0)
  lastLoginIp          String?
  lastLoginUserAgent   String?
  loginAttempts        Int                 @default(0)
  lastFailedLoginAt    DateTime?
  userSessions         UserSession[]
  fraudReports         FraudReport[]       @relation("ReporterUser")
  reportedFraudReports FraudReport[]       @relation("ReportedUser")
  reviewedFraudReports FraudReport[]       @relation("ReviewerUser")
  suspiciousActivities SuspiciousActivity[] @relation("UserActivities")
  resolvedActivities   SuspiciousActivity[] @relation("ResolverUser")
  savedSearches        SavedSearch[]
  shareEvents          ShareEvent[]
  locations            UserLocation[]
  currentLocationId    String?
  currentLocation      UserLocation?       @relation("CurrentLocation", fields: [currentLocationId], references: [id])
  productViews         ProductView[]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Product {
  id           String        @id @default(cuid())
  name         String
  description  String?
  price        Float
  images       String[]      @default([])
  condition    ProductCondition @default(USED)
  status       ProductStatus @default(ACTIVE)
  viewCount    Int           @default(0)
  featuredUntil DateTime?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  seller       User          @relation(fields: [sellerId], references: [id])
  sellerId     String
  orders       Order[]
  reviews      Review[]
  category     Category      @relation(fields: [categoryId], references: [id])
  categoryId   String
  orderItems   OrderItem[]
  chats        Chat[]
  riskScore    Int           @default(0)
  isHidden     Boolean       @default(false)
  hiddenReason String?
  fraudReports FraudReport[]
  shareCount   Int           @default(0)
  shareEvents  ShareEvent[]
  location     Location?     @relation(fields: [locationId], references: [id])
  locationId   String?
  views        ProductView[]
}

model Category {
  id            String      @id @default(cuid())
  name          String
  slug          String      @unique
  icon          String
  description   String?
  products      Product[]
  parentId      String?
  parent        Category?   @relation("SubCategories", fields: [parentId], references: [id])
  subcategories Category[]  @relation("SubCategories")
  attributes    CategoryAttribute[]
}

model Review {
  id         String   @id @default(cuid())
  rating     Int
  comment    String?
  createdAt  DateTime @default(now())
  product    Product  @relation(fields: [productId], references: [id])
  productId  String
  reviewer   User     @relation(fields: [reviewerId], references: [id])
  reviewerId String
}

model Seller {
  id                   String   @id @default(cuid())
  userId               String   @unique
  businessName         String?
  description          String?
  phoneNumber          String?
  address              String?
  rating               Float    @default(0)
  totalSales           Int      @default(0)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  user                 User     @relation(fields: [userId], references: [id])
  phoneVerified        Boolean  @default(false)
  idVerified           Boolean  @default(false)
  idVerificationDate   DateTime?
  idVerificationMethod String?
  idVerificationStatus VerificationStatus @default(PENDING)
  socialAccounts       SocialAccount[]
}

enum TransactionStatus {
  INQUIRY
  AGREEMENT
  PAYMENT_PENDING
  PREPARING
  SHIPPING
  DELIVERED
  CANCELLED
}

model Order {
  id          String      @id @default(cuid())
  status      TransactionStatus @default(INQUIRY)
  totalAmount Float
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  product     Product     @relation(fields: [productId], references: [id])
  productId   String
  buyer       User        @relation(fields: [buyerId], references: [id])
  buyerId     String
  items       OrderItem[]
  statusHistory StatusHistory[]
  estimatedDeliveryDate DateTime?
  trackingNumber String?
  notes String?
  disputes    Dispute[]
}

model StatusHistory {
  id        String   @id @default(cuid())
  status    TransactionStatus
  createdAt DateTime @default(now())
  updatedBy String
  notes     String?
  order     Order    @relation(fields: [orderId], references: [id])
  orderId   String
}

model OrderItem {
  id        String   @id @default(cuid())
  quantity  Int
  price     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  order     Order    @relation(fields: [orderId], references: [id])
  orderId   String
  product   Product  @relation(fields: [productId], references: [id])
  productId String
}

model Settings {
  id                    Int      @id @default(1)
  siteName             String   @default("AlanCash Marketplace")
  siteDescription      String   @default("Your trusted marketplace for buying and selling")
  maintenanceMode      Boolean  @default(false)
  allowNewRegistrations Boolean  @default(true)
  requireEmailVerification Boolean @default(true)
  maxProductsPerSeller Int      @default(100)
  commissionRate       Float    @default(5)
  currency             String   @default("USD")
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
}

enum Role {
  GUEST
  USER
  ADMIN
}

enum ProductCondition {
  NEW
  USED
  REFURBISHED
}

enum ProductStatus {
  DRAFT
  ACTIVE
  SOLD
  INACTIVE
}

enum VerificationStatus {
  PENDING
  VERIFIED
  REJECTED
}

model Follow {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())
  follower    User     @relation("Followers", fields: [followerId], references: [id])
  followerId  String
  following   User     @relation("Following", fields: [followingId], references: [id])
  followingId String

  @@unique([followerId, followingId])
}

model Badge {
  id          String   @id @default(cuid())
  name        String
  description String
  icon        String
  earnedAt    DateTime @default(now())
  user        User     @relation(fields: [userId], references: [id])
  userId      String
}

model Achievement {
  id          String   @id @default(cuid())
  name        String
  description String
  progress    Int      @default(0)
  target      Int
  completed   Boolean  @default(false)
  reward      String
  user        User     @relation(fields: [userId], references: [id])
  userId      String
}

model Notification {
  id        String   @id @default(cuid())
  type      String
  message   String
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
}

model Chat {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  product   Product?  @relation(fields: [productId], references: [id])
  productId String?
  buyer     User      @relation("BuyerChats", fields: [buyerId], references: [id])
  buyerId   String
  seller    User      @relation("SellerChats", fields: [sellerId], references: [id])
  sellerId  String
  messages  Message[]
  status    ChatStatus @default(ACTIVE)

  @@unique([buyerId, sellerId, productId])
}

model Message {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  chat      Chat     @relation(fields: [chatId], references: [id])
  chatId    String
  sender    User     @relation(fields: [senderId], references: [id])
  senderId  String
  read      Boolean  @default(false)
  attachments MessageAttachment[]
}

model MessageAttachment {
  id        String   @id @default(cuid())
  type      String   // 'image', 'file', etc.
  url       String
  name      String
  size      Int
  createdAt DateTime @default(now())
  message   Message  @relation(fields: [messageId], references: [id])
  messageId String
}

enum ChatStatus {
  ACTIVE
  ARCHIVED
  BLOCKED
}

model SocialAccount {
  id        String   @id @default(cuid())
  provider  String   // e.g., 'facebook', 'twitter', 'instagram', 'linkedin'
  handle    String   // username or handle on the platform
  url       String?  // profile URL
  verified  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  seller    Seller   @relation(fields: [sellerId], references: [id])
  sellerId  String

  @@unique([provider, sellerId])
}

enum VerificationType {
  PHONE
  ID
  SOCIAL
}

enum DisputeStatus {
  OPEN
  UNDER_REVIEW
  RESOLVED
  CLOSED
}

enum DisputeReason {
  ITEM_NOT_RECEIVED
  ITEM_NOT_AS_DESCRIBED
  DAMAGED_ITEM
  WRONG_ITEM
  OTHER
}

model Dispute {
  id            String          @id @default(cuid())
  reason        DisputeReason
  description   String
  status        DisputeStatus   @default(OPEN)
  resolution    String?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  order         Order           @relation(fields: [orderId], references: [id])
  orderId       String
  openedBy      User            @relation("OpenedDisputes", fields: [openedById], references: [id])
  openedById    String
  assignedTo    User?           @relation("AssignedDisputes", fields: [assignedToId], references: [id])
  assignedToId  String?
  messages      DisputeMessage[]
  evidences     DisputeEvidence[]
}

model DisputeMessage {
  id             String   @id @default(cuid())
  content        String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  dispute        Dispute  @relation(fields: [disputeId], references: [id], onDelete: Cascade)
  disputeId      String
  sender         User     @relation(fields: [senderId], references: [id])
  senderId       String
  isAdminMessage Boolean  @default(false)
}

model DisputeEvidence {
  id           String   @id @default(cuid())
  type         String   // 'image', 'document', 'video', etc.
  url          String
  description  String?
  createdAt    DateTime @default(now())
  dispute      Dispute  @relation(fields: [disputeId], references: [id], onDelete: Cascade)
  disputeId    String
  uploadedBy   User     @relation(fields: [uploadedById], references: [id])
  uploadedById String
}

enum FraudReportStatus {
  PENDING
  UNDER_REVIEW
  RESOLVED
  DISMISSED
}

enum FraudReportReason {
  COUNTERFEIT
  MISLEADING
  PROHIBITED_ITEM
  SCAM
  OTHER
}

model UserSession {
  id          String    @id @default(cuid())
  userId      String
  ipAddress   String
  userAgent   String?
  deviceId    String?
  location    String?
  createdAt   DateTime  @default(now())
  expiresAt   DateTime
  isRevoked   Boolean   @default(false)
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model FraudReport {
  id           String            @id @default(cuid())
  reason       FraudReportReason
  description  String
  status       FraudReportStatus @default(PENDING)
  resolution   String?
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  reporter     User              @relation("ReporterUser", fields: [reporterId], references: [id])
  reporterId   String
  product      Product?          @relation(fields: [productId], references: [id])
  productId    String?
  seller       User?             @relation("ReportedUser", fields: [sellerId], references: [id])
  sellerId     String?
  reviewedBy   User?             @relation("ReviewerUser", fields: [reviewedById], references: [id])
  reviewedById String?
  reviewedAt   DateTime?
}

model SuspiciousActivity {
  id           String    @id @default(cuid())
  type         String
  description  String
  severity     Int       @default(1)
  createdAt    DateTime  @default(now())
  user         User      @relation("UserActivities", fields: [userId], references: [id])
  userId       String
  ipAddress    String?
  userAgent    String?
  isResolved   Boolean   @default(false)
  resolvedBy   User?     @relation("ResolverUser", fields: [resolvedById], references: [id])
  resolvedById String?
  resolvedAt   DateTime?
  notes        String?
}

model SavedSearch {
  id          String   @id @default(cuid())
  name        String
  filters     Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String
  isDefault   Boolean  @default(false)
  lastUsed    DateTime?

  @@unique([userId, name])
}

model ShareEvent {
  id          String    @id @default(cuid())
  url         String
  platform    String
  createdAt   DateTime  @default(now())
  userId      String?
  user        User?     @relation(fields: [userId], references: [id])
  productId   String?
  product     Product?  @relation(fields: [productId], references: [id])
  userAgent   String?
  referrer    String?
}

model CategoryAttribute {
  id         String   @id @default(cuid())
  name       String
  type       String
  options    String[]
  isRequired Boolean  @default(false)
  category   Category @relation(fields: [categoryId], references: [id])
  categoryId String

  @@unique([categoryId, name])
}

model Location {
  id          String    @id @default(cuid())
  address     String
  city        String
  state       String?
  country     String
  postalCode  String?
  latitude    Float
  longitude   Float
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]

  @@index([latitude, longitude])
}

model UserLocation {
  id          String    @id @default(cuid())
  name        String    // e.g., "Home", "Work", etc.
  address     String
  city        String
  state       String?
  country     String
  postalCode  String?
  latitude    Float
  longitude   Float
  isDefault   Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String
  currentForUsers User[] @relation("CurrentLocation")

  @@index([latitude, longitude])
  @@index([userId])
}

model VehicleMakeModel {
  id        String   @id @default(cuid())
  make      String
  model     String
  category  String   // 'car', 'bus', or 'motorcycle'
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([make, model, category])
}

model ProductView {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId String
  ipAddress String?
  userAgent String?

  @@unique([userId, productId])
  @@index([productId])
  @@index([userId])
}
