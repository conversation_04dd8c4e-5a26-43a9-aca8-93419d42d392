import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const carMakeModels = [
  // Toyota
  { make: 'Toyota', models: ['<PERSON><PERSON>', 'Corolla', 'RAV4', 'Highlander', 'Sienna', 'Tacoma', 'Tundra', '4Runner', 'Land Cruiser', 'Prius'] },
  // Honda
  { make: 'Honda', models: ['Civic', 'Accord', 'CR-V', 'Pilot', 'Odyssey', 'HR-V', 'Ridgeline', 'Passport', 'Insight', 'Fit'] },
  // Ford
  { make: 'Ford', models: ['F-150', 'Mustang', 'Explorer', 'Escape', 'Edge', 'Ranger', 'Bronco', 'Expedition', 'Maverick', 'Fusion'] },
  // BMW
  { make: 'BMW', models: ['3 Series', '5 Series', 'X3', 'X5', '7 Series', 'X1', 'X7', 'M3', 'M5', 'iX'] },
  // Mercedes-Benz
  { make: 'Mercedes-Benz', models: ['C-Class', 'E-Class', 'S-Class', 'GLC', 'GLE', 'A-Class', 'CLA', 'GLA', 'G-Class', 'AMG GT'] },
  // Audi
  { make: 'Audi', models: ['A4', 'A6', 'Q5', 'Q7', 'A3', 'Q3', 'e-tron', 'RS', 'S4', 'S6'] },
  // Volkswagen
  { make: 'Volkswagen', models: ['Golf', 'Passat', 'Tiguan', 'Atlas', 'Jetta', 'Arteon', 'ID.4', 'Taos', 'T-Roc', 'T-Cross'] },
  // Hyundai
  { make: 'Hyundai', models: ['Elantra', 'Sonata', 'Tucson', 'Santa Fe', 'Palisade', 'Kona', 'Venue', 'Ioniq', 'Nexo', 'Staria'] },
  // Kia
  { make: 'Kia', models: ['Sorento', 'Sportage', 'Telluride', 'Forte', 'K5', 'Soul', 'Carnival', 'EV6', 'Niro', 'Stinger'] },
  // Nissan
  { make: 'Nissan', models: ['Altima', 'Rogue', 'Murano', 'Pathfinder', 'Frontier', 'Titan', 'Maxima', 'Sentra', 'Kicks', 'Leaf'] },
];

const busMakeModels = [
  // Mercedes-Benz
  { make: 'Mercedes-Benz', models: ['Sprinter', 'O500', 'O400', 'O350', 'Citaro', 'Conecto', 'Integro', 'Capacito', 'Citaro G', 'eCitaro'] },
  // Volvo
  { make: 'Volvo', models: ['B8R', 'B9R', 'B11R', 'B12R', 'B13R', '7900', '8900', '9700', '9900', '7900 Electric'] },
  // Scania
  { make: 'Scania', models: ['K Series', 'P Series', 'G Series', 'R Series', 'Citywide', 'Interlink', 'OmniExpress', 'OmniLink', 'OmniCity', 'OmniExpress'] },
  // MAN
  { make: 'MAN', models: ['Lions Coach', 'Lions City', 'Lions Intercity', 'Lions Coach C', 'Lions City G', 'Lions City E', 'Lions Intercity LE', 'Lions Coach LE', 'Lions City LE', 'Lions Intercity LE'] },
  // Iveco
  { make: 'Iveco', models: ['Crossway', 'Magelys', 'Evadys', 'Urbanway', 'Crealis', 'Arway', 'E-Way', 'Daily', 'Eurocargo', 'Stralis'] },
  // Yutong
  { make: 'Yutong', models: ['ZK6122H9', 'ZK6128HGA', 'ZK6128HGE', 'ZK6128HGB', 'ZK6128HGC', 'ZK6128HGD', 'ZK6128HGF', 'ZK6128HGG', 'ZK6128HGH', 'ZK6128HGI'] },
  // Higer
  { make: 'Higer', models: ['KLQ6129', 'KLQ6858', 'KLQ6109', 'KLQ6125', 'KLQ6128', 'KLQ6129', 'KLQ6858', 'KLQ6109', 'KLQ6125', 'KLQ6128'] },
  // King Long
  { make: 'King Long', models: ['XML6125', 'XML6127', 'XML6128', 'XML6129', 'XML6858', 'XML6109', 'XML6125', 'XML6127', 'XML6128', 'XML6129'] },
];

const motorcycleMakeModels = [
  // Honda
  { make: 'Honda', models: ['CBR1000RR', 'CB1000R', 'CB650R', 'CBR650R', 'CB500F', 'CBR500R', 'NC750X', 'Africa Twin', 'Gold Wing', 'Forza'] },
  // Yamaha
  { make: 'Yamaha', models: ['MT-10', 'MT-09', 'MT-07', 'R1', 'R6', 'Tracer 9', 'Tracer 7', 'Tenere 700', 'XSR900', 'Niken'] },
  // Suzuki
  { make: 'Suzuki', models: ['GSX-R1000', 'GSX-R750', 'GSX-R600', 'GSX-S1000', 'GSX-S750', 'V-Strom 1050', 'V-Strom 650', 'Hayabusa', 'Katana', 'Burgman'] },
  // Kawasaki
  { make: 'Kawasaki', models: ['Ninja ZX-10R', 'Ninja ZX-6R', 'Ninja 1000SX', 'Ninja 650', 'Z900', 'Z650', 'Versys 1000', 'Versys 650', 'Concours 14', 'W800'] },
  // BMW
  { make: 'BMW', models: ['S1000RR', 'S1000R', 'S1000XR', 'R1250GS', 'R1250RT', 'F900R', 'F900XR', 'F850GS', 'K1600GT', 'R18'] },
  // Ducati
  { make: 'Ducati', models: ['Panigale V4', 'Streetfighter V4', 'Multistrada V4', 'Monster', 'Diavel', 'XDiavel', 'Hypermotard', 'Scrambler', 'SuperSport', 'Streetfighter V2'] },
  // Harley-Davidson
  { make: 'Harley-Davidson', models: ['Street Bob', 'Fat Bob', 'Low Rider', 'Road King', 'Road Glide', 'Street Glide', 'Heritage Classic', 'Softail Standard', 'Iron 883', 'Sport Glide'] },
  // KTM
  { make: 'KTM', models: ['1290 Super Duke R', '890 Duke', '790 Duke', '390 Duke', '1290 Super Adventure S', '890 Adventure', '790 Adventure', '690 SMC R', '450 SX-F', '350 EXC-F'] },
];

async function main() {
  // Seed car make-models
  for (const { make, models } of carMakeModels) {
    for (const model of models) {
      await prisma.vehicleMakeModel.upsert({
        where: {
          make_model_category: {
            make,
            model,
            category: 'car',
          },
        },
        update: {},
        create: {
          make,
          model,
          category: 'car',
        },
      });
    }
  }

  // Seed bus make-models
  for (const { make, models } of busMakeModels) {
    for (const model of models) {
      await prisma.vehicleMakeModel.upsert({
        where: {
          make_model_category: {
            make,
            model,
            category: 'bus',
          },
        },
        update: {},
        create: {
          make,
          model,
          category: 'bus',
        },
      });
    }
  }

  // Seed motorcycle make-models
  for (const { make, models } of motorcycleMakeModels) {
    for (const model of models) {
      await prisma.vehicleMakeModel.upsert({
        where: {
          make_model_category: {
            make,
            model,
            category: 'motorcycle',
          },
        },
        update: {},
        create: {
          make,
          model,
          category: 'motorcycle',
        },
      });
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 