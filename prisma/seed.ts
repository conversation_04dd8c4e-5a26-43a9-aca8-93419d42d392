import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Create Vehicle parent category
  const vehicleCategory = await prisma.category.upsert({
    where: { slug: 'vehicles' },
    update: {},
    create: {
      name: 'Vehicles',
      slug: 'vehicles',
      icon: '🚗',
      description: 'All types of vehicles including cars, buses, motorcycles, and more',
    },
  });

  // Create Car subcategory
  const carCategory = await prisma.category.upsert({
    where: { slug: 'cars' },
    update: {},
    create: {
      name: 'Cars',
      slug: 'cars',
      icon: '🚙',
      description: 'Personal and commercial cars',
      parentId: vehicleCategory.id,
    },
  });

  // Create Bus subcategory
  const busCategory = await prisma.category.upsert({
    where: { slug: 'buses' },
    update: {},
    create: {
      name: 'Buses',
      slug: 'buses',
      icon: '🚌',
      description: 'Commercial and passenger buses',
      parentId: vehicleCategory.id,
    },
  });

  // Create Motorcycle subcategory
  const motorcycleCategory = await prisma.category.upsert({
    where: { slug: 'motorcycles' },
    update: {},
    create: {
      name: 'Motorcycles',
      slug: 'motorcycles',
      icon: '🏍️',
      description: 'Motorcycles and scooters',
      parentId: vehicleCategory.id,
    },
  });

  // Create attributes for Cars category
  const carAttributes = [
    {
      name: 'Make',
      type: 'select',
      options: ['Toyota', 'Honda', 'Ford', 'BMW', 'Mercedes-Benz', 'Audi', 'Volkswagen', 'Hyundai', 'Kia', 'Nissan'],
      isRequired: true,
    },
    {
      name: 'Model',
      type: 'select',
      options: [], // Will be populated dynamically based on Make
      isRequired: true,
    },
    {
      name: 'Year',
      type: 'number',
      options: [],
      isRequired: true,
    },
    {
      name: 'Condition',
      type: 'select',
      options: ['New', 'Used', 'Certified Pre-owned'],
      isRequired: true,
    },
    {
      name: 'Mileage',
      type: 'number',
      options: [],
      isRequired: true,
    },
    {
      name: 'Fuel Type',
      type: 'select',
      options: ['Petrol', 'Diesel', 'Electric', 'Hybrid'],
      isRequired: true,
    },
    {
      name: 'Transmission',
      type: 'select',
      options: ['Automatic', 'Manual'],
      isRequired: true,
    },
    {
      name: 'Color',
      type: 'select',
      options: ['Black', 'White', 'Silver', 'Gray', 'Red', 'Blue', 'Green', 'Yellow', 'Other'],
      isRequired: true,
    },
  ];

  // Create attributes for the Cars category
  for (const attr of carAttributes) {
    await prisma.categoryAttribute.upsert({
      where: {
        categoryId_name: {
          categoryId: carCategory.id,
          name: attr.name,
        },
      },
      update: {
        type: attr.type,
        options: attr.options,
        isRequired: attr.isRequired,
      },
      create: {
        name: attr.name,
        type: attr.type,
        options: attr.options,
        isRequired: attr.isRequired,
        categoryId: carCategory.id,
      },
    });
  }

  // Create attributes for Buses category
  const busAttributes = [
    {
      name: 'Make',
      type: 'select',
      options: ['Mercedes-Benz', 'Volvo', 'Scania', 'MAN', 'Iveco', 'Yutong', 'Higer', 'King Long'],
      isRequired: true,
    },
    {
      name: 'Model',
      type: 'select',
      options: [], // Will be populated dynamically based on Make
      isRequired: true,
    },
    {
      name: 'Year',
      type: 'number',
      options: [],
      isRequired: true,
    },
    {
      name: 'Capacity',
      type: 'number',
      options: [],
      isRequired: true,
    },
    {
      name: 'Fuel Type',
      type: 'select',
      options: ['Diesel', 'Electric', 'CNG'],
      isRequired: true,
    },
  ];

  // Create attributes for the Buses category
  for (const attr of busAttributes) {
    await prisma.categoryAttribute.upsert({
      where: {
        categoryId_name: {
          categoryId: busCategory.id,
          name: attr.name,
        },
      },
      update: {
        type: attr.type,
        options: attr.options,
        isRequired: attr.isRequired,
      },
      create: {
        name: attr.name,
        type: attr.type,
        options: attr.options,
        isRequired: attr.isRequired,
        categoryId: busCategory.id,
      },
    });
  }

  // Create attributes for Motorcycles category
  const motorcycleAttributes = [
    {
      name: 'Make',
      type: 'select',
      options: ['Honda', 'Yamaha', 'Suzuki', 'Kawasaki', 'BMW', 'Ducati', 'Harley-Davidson', 'KTM'],
      isRequired: true,
    },
    {
      name: 'Model',
      type: 'select',
      options: [], // Will be populated dynamically based on Make
      isRequired: true,
    },
    {
      name: 'Year',
      type: 'number',
      options: [],
      isRequired: true,
    },
    {
      name: 'Engine Capacity',
      type: 'number',
      options: [],
      isRequired: true,
    },
    {
      name: 'Condition',
      type: 'select',
      options: ['New', 'Used'],
      isRequired: true,
    },
  ];

  // Create attributes for the Motorcycles category
  for (const attr of motorcycleAttributes) {
    await prisma.categoryAttribute.upsert({
      where: {
        categoryId_name: {
          categoryId: motorcycleCategory.id,
          name: attr.name,
        },
      },
      update: {
        type: attr.type,
        options: attr.options,
        isRequired: attr.isRequired,
      },
      create: {
        name: attr.name,
        type: attr.type,
        options: attr.options,
        isRequired: attr.isRequired,
        categoryId: motorcycleCategory.id,
      },
    });
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 