import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function seedDemoData() {
  try {
    console.log('🌱 Starting demo data seeding...');

    // Create demo users
    const hashedPassword = await bcrypt.hash('demo123', 12);
    
    const demoUsers = await Promise.all([
      prisma.user.create({
        data: {
          name: '<PERSON>',
          email: '<EMAIL>',
          hashedPassword,
          role: 'USER',
        },
      }),
      prisma.user.create({
        data: {
          name: '<PERSON>',
          email: '<EMAIL>',
          hashedPassword,
          role: 'USER',
        },
      }),
      prisma.user.create({
        data: {
          name: '<PERSON>',
          email: '<EMAIL>',
          hashedPassword,
          role: 'USER',
        },
      }),
      prisma.user.create({
        data: {
          name: '<PERSON>',
          email: '<EMAIL>',
          hashedPassword,
          role: 'USER',
        },
      }),
    ]);

    console.log('✅ Created demo users');

    // Create seller profiles for some users
    const sellers = await Promise.all([
      prisma.seller.create({
        data: {
          userId: demoUsers[0].id,
          businessName: 'John\'s Electronics',
          description: 'Quality electronics and gadgets at great prices',
          phoneVerified: true,
          idVerified: true,
          idVerificationStatus: 'VERIFIED',
          rating: 4.8,
        },
      }),
      prisma.seller.create({
        data: {
          userId: demoUsers[1].id,
          businessName: 'Sarah\'s Fashion',
          description: 'Trendy clothing and accessories for all occasions',
          phoneVerified: true,
          idVerified: false,
          idVerificationStatus: 'PENDING',
          rating: 4.5,
        },
      }),
      prisma.seller.create({
        data: {
          userId: demoUsers[2].id,
          businessName: 'Mike\'s Auto Parts',
          description: 'Reliable auto parts and accessories',
          phoneVerified: true,
          idVerified: true,
          idVerificationStatus: 'VERIFIED',
          rating: 4.9,
        },
      }),
    ]);

    console.log('✅ Created seller profiles');

    // Get existing categories
    const categories = await prisma.category.findMany();
    if (categories.length === 0) {
      console.log('❌ No categories found. Please run category seeding first.');
      return;
    }

    // Create demo products
    const products = [
      {
        name: 'iPhone 14 Pro Max',
        description: 'Latest iPhone in excellent condition. 256GB storage, unlocked.',
        price: 899.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/788946/pexels-photo-788946.jpeg',
          'https://images.pexels.com/photos/1092644/pexels-photo-1092644.jpeg'
        ],
        sellerId: demoUsers[0].id,
        categoryId: categories.find(c => c.name.includes('Electronics'))?.id || categories[0].id,
        viewCount: 45,
      },
      {
        name: 'MacBook Air M2',
        description: 'Brand new MacBook Air with M2 chip. Perfect for work and study.',
        price: 1199.99,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/205421/pexels-photo-205421.jpeg',
          'https://images.pexels.com/photos/1029757/pexels-photo-1029757.jpeg'
        ],
        sellerId: demoUsers[0].id,
        categoryId: categories.find(c => c.name.includes('Electronics'))?.id || categories[0].id,
        viewCount: 67,
      },
      {
        name: 'Designer Handbag',
        description: 'Authentic designer handbag in perfect condition. Comes with certificate.',
        price: 450.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/1152077/pexels-photo-1152077.jpeg',
          'https://images.pexels.com/photos/1464625/pexels-photo-1464625.jpeg'
        ],
        sellerId: demoUsers[1].id,
        categoryId: categories.find(c => c.name.includes('Fashion'))?.id || categories[0].id,
        viewCount: 23,
      },
      {
        name: 'Vintage Leather Jacket',
        description: 'Classic vintage leather jacket. Size M. Great for any season.',
        price: 120.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/1124465/pexels-photo-1124465.jpeg'
        ],
        sellerId: demoUsers[1].id,
        categoryId: categories.find(c => c.name.includes('Fashion'))?.id || categories[0].id,
        viewCount: 34,
      },
      {
        name: 'Car Brake Pads Set',
        description: 'High-quality brake pads for Toyota Camry 2018-2022. Brand new.',
        price: 89.99,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/3806288/pexels-photo-3806288.jpeg'
        ],
        sellerId: demoUsers[2].id,
        categoryId: categories.find(c => c.name.includes('Vehicles'))?.id || categories[0].id,
        viewCount: 12,
      },
      {
        name: 'Gaming Chair',
        description: 'Ergonomic gaming chair with RGB lighting. Very comfortable.',
        price: 299.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/4050315/pexels-photo-4050315.jpeg'
        ],
        sellerId: demoUsers[0].id,
        categoryId: categories.find(c => c.name.includes('Furniture'))?.id || categories[0].id,
        viewCount: 56,
      },
      {
        name: 'Wireless Headphones',
        description: 'Sony WH-1000XM4 noise-canceling headphones. Excellent sound quality.',
        price: 249.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/3394650/pexels-photo-3394650.jpeg'
        ],
        sellerId: demoUsers[0].id,
        categoryId: categories.find(c => c.name.includes('Electronics'))?.id || categories[0].id,
        viewCount: 78,
      },
      {
        name: 'Mountain Bike',
        description: 'Trek mountain bike in great condition. Perfect for trails.',
        price: 650.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/100582/pexels-photo-100582.jpeg'
        ],
        sellerId: demoUsers[3].id,
        categoryId: categories.find(c => c.name.includes('Sports'))?.id || categories[0].id,
        viewCount: 29,
      },
    ];

    const createdProducts = await Promise.all(
      products.map(product => prisma.product.create({ data: product }))
    );

    console.log('✅ Created demo products');

    // Create some product views
    const productViews = [];
    for (const product of createdProducts) {
      for (const user of demoUsers) {
        if (Math.random() > 0.5) { // 50% chance of viewing
          productViews.push({
            userId: user.id,
            productId: product.id,
          });
        }
      }
    }

    await Promise.all(
      productViews.map(view => 
        prisma.productView.create({ data: view }).catch(() => {
          // Ignore unique constraint errors
        })
      )
    );

    console.log('✅ Created product views');

    console.log('🎉 Demo data seeding completed successfully!');
    console.log(`Created:
    - ${demoUsers.length} demo users
    - ${sellers.length} seller profiles  
    - ${createdProducts.length} demo products
    - ${productViews.length} product views`);

  } catch (error) {
    console.error('❌ Error seeding demo data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedDemoData();
