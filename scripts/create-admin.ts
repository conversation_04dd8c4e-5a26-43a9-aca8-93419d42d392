import { PrismaClient, Role } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function main() {
  const email = '<EMAIL>';
  const password = 'Admin123';

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    console.log('User already exists, updating password and role...');
    const hashedPassword = await bcrypt.hash(password, 10);
    await prisma.user.update({
      where: { email },
      data: {
        hashedPassword: hashedPassword,
        role: Role.ADMIN
      }
    });
    console.log('Password and role updated successfully');
  } else {
    console.log('Creating new admin user...');
    const hashedPassword = await bcrypt.hash(password, 10);
    await prisma.user.create({
      data: {
        email,
        name: '<PERSON><PERSON> User',
        hashedPassword: hashedPassword,
        role: Role.ADMIN
      }
    });
    console.log('Admin user created successfully');
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });