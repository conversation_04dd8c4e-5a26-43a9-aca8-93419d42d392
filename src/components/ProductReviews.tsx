'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { StarIcon } from '@heroicons/react/24/solid';
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';
import { PlusIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import ReviewCard from './ReviewCard';
import ReviewForm from './ReviewForm';

interface Review {
  id: string;
  rating: number;
  comment: string | null;
  createdAt: string;
  reviewer: {
    id: string;
    name: string | null;
    image: string | null;
  };
}

interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

interface ProductReviewsProps {
  productId: string;
  sellerId: string;
}

export default function ProductReviews({ productId, sellerId }: ProductReviewsProps) {
  const { data: session } = useSession();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats>({
    averageRating: 0,
    totalReviews: 0,
    ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
  });
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingReview, setEditingReview] = useState<Review | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const isOwner = session?.user?.id === sellerId;
  const userReview = reviews.find(review => review.reviewer.id === session?.user?.id);

  useEffect(() => {
    fetchReviews();
  }, [productId]);

  const fetchReviews = async (pageNum = 1, reset = true) => {
    try {
      const response = await fetch(`/api/products/${productId}/reviews?page=${pageNum}&limit=10`);
      if (!response.ok) throw new Error('Failed to fetch reviews');
      
      const data = await response.json();
      
      if (reset) {
        setReviews(data.reviews);
      } else {
        setReviews(prev => [...prev, ...data.reviews]);
      }
      
      setStats(data.stats);
      setHasMore(data.pagination.page < data.pagination.totalPages);
      setPage(pageNum);
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitReview = async (data: { rating: number; comment: string }) => {
    setIsSubmitting(true);
    try {
      const url = editingReview 
        ? `/api/reviews/${editingReview.id}`
        : `/api/products/${productId}/reviews`;
      
      const method = editingReview ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit review');
      }

      // Refresh reviews
      await fetchReviews();
      setShowForm(false);
      setEditingReview(null);
    } catch (error) {
      console.error('Error submitting review:', error);
      alert(error instanceof Error ? error.message : 'Failed to submit review');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteReview = async (reviewId: string) => {
    try {
      const response = await fetch(`/api/reviews/${reviewId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete review');
      }

      // Refresh reviews
      await fetchReviews();
    } catch (error) {
      console.error('Error deleting review:', error);
      alert('Failed to delete review');
    }
  };

  const handleEditReview = (review: Review) => {
    setEditingReview(review);
    setShowForm(true);
  };

  const loadMoreReviews = () => {
    fetchReviews(page + 1, false);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Reviews & Feedback</h2>
            <div className="flex items-center mt-2">
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <div key={star}>
                    {star <= Math.round(stats.averageRating) ? (
                      <StarIcon className="w-5 h-5 text-yellow-400" />
                    ) : (
                      <StarOutlineIcon className="w-5 h-5 text-gray-300" />
                    )}
                  </div>
                ))}
              </div>
              <span className="ml-2 text-gray-600">
                {stats.averageRating.toFixed(1)} ({stats.totalReviews} reviews)
              </span>
            </div>
          </div>

          {/* Add Review Button */}
          {session && !isOwner && !userReview && !showForm && (
            <button
              onClick={() => setShowForm(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              <PlusIcon className="w-4 h-4" />
              <span>Write Review</span>
            </button>
          )}
        </div>
      </div>

      {/* Review Form */}
      {showForm && (
        <div className="p-6 border-b border-gray-200">
          <ReviewForm
            productId={productId}
            existingReview={editingReview || undefined}
            onSubmit={handleSubmitReview}
            onCancel={() => {
              setShowForm(false);
              setEditingReview(null);
            }}
            isSubmitting={isSubmitting}
          />
        </div>
      )}

      {/* Reviews List */}
      <div className="p-6">
        {reviews.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No reviews yet. Be the first to review this product!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {reviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
                onEdit={handleEditReview}
                onDelete={handleDeleteReview}
              />
            ))}

            {/* Load More Button */}
            {hasMore && (
              <div className="text-center pt-4">
                <button
                  onClick={loadMoreReviews}
                  className="flex items-center space-x-2 mx-auto px-4 py-2 text-indigo-600 border border-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors"
                >
                  <ChevronDownIcon className="w-4 h-4" />
                  <span>Load More Reviews</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
