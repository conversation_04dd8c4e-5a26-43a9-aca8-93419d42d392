import React, { useState, useEffect } from 'react';
import { HiChevronDown } from 'react-icons/hi';

interface VehicleMakeModelSelectProps {
  category: string;
  onMakeChange: (make: string) => void;
  onModelChange: (model: string) => void;
  selectedMake?: string;
  selectedModel?: string;
  className?: string;
}

export default function VehicleMakeModelSelect({
  category,
  onMakeChange,
  onModelChange,
  selectedMake,
  selectedModel,
  className = '',
}: VehicleMakeModelSelectProps) {
  const [makes, setMakes] = useState<string[]>([]);
  const [models, setModels] = useState<string[]>([]);
  const [isLoadingMakes, setIsLoadingMakes] = useState(false);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  // Fetch makes when component mounts or category changes
  useEffect(() => {
    const fetchMakes = async () => {
      try {
        setIsLoadingMakes(true);
        const response = await fetch(`/api/vehicles/makes?category=${category}`);
        if (!response.ok) throw new Error('Failed to fetch makes');
        const data = await response.json();
        setMakes(data);
      } catch (error) {
        console.error('Error fetching makes:', error);
      } finally {
        setIsLoadingMakes(false);
      }
    };

    fetchMakes();
  }, [category]);

  // Fetch models when make changes
  useEffect(() => {
    const fetchModels = async () => {
      if (!selectedMake) {
        setModels([]);
        return;
      }

      try {
        setIsLoadingModels(true);
        const response = await fetch(
          `/api/vehicles/models?make=${selectedMake}&category=${category}`
        );
        if (!response.ok) throw new Error('Failed to fetch models');
        const data = await response.json();
        setModels(data);
      } catch (error) {
        console.error('Error fetching models:', error);
      } finally {
        setIsLoadingModels(false);
      }
    };

    fetchModels();
  }, [selectedMake, category]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Make Select */}
      <div>
        <label
          htmlFor="make"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Make
        </label>
        <div className="relative">
          <select
            id="make"
            value={selectedMake || ''}
            onChange={(e) => onMakeChange(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            disabled={isLoadingMakes}
          >
            <option value="">Select Make</option>
            {makes.map((make) => (
              <option key={make} value={make}>
                {make}
              </option>
            ))}
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <HiChevronDown className="h-5 w-5" />
          </div>
        </div>
      </div>

      {/* Model Select */}
      <div>
        <label
          htmlFor="model"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Model
        </label>
        <div className="relative">
          <select
            id="model"
            value={selectedModel || ''}
            onChange={(e) => onModelChange(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            disabled={isLoadingModels || !selectedMake}
          >
            <option value="">Select Model</option>
            {models.map((model) => (
              <option key={model} value={model}>
                {model}
              </option>
            ))}
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <HiChevronDown className="h-5 w-5" />
          </div>
        </div>
      </div>
    </div>
  );
} 