'use client';

import { useState } from 'react';
import { 
  ShieldCheckIcon, 
  ExclamationTriangleIcon, 
  EyeIcon, 
  DocumentCheckIcon,
  MapPinIcon,
  CreditCardIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';

interface SafetyTip {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  color: string;
}

const safetyTips: SafetyTip[] = [
  {
    id: 'payment',
    icon: CreditCardIcon,
    title: 'Never send prepayments',
    description: 'Avoid sending money, gift cards, or cryptocurrency before seeing the item in person. Legitimate sellers will accept payment upon delivery or pickup.',
    color: 'text-red-600 bg-red-50 border-red-200',
  },
  {
    id: 'meeting',
    icon: MapPinIcon,
    title: 'Meet in safe public places',
    description: 'Choose well-lit, busy public locations like shopping centers, coffee shops, or police station parking lots. Bring a friend if possible.',
    color: 'text-blue-600 bg-blue-50 border-blue-200',
  },
  {
    id: 'inspection',
    icon: EyeIcon,
    title: 'Inspect before you buy',
    description: 'Thoroughly examine the item to ensure it matches the description and photos. Test electronics and check for any damage or defects.',
    color: 'text-green-600 bg-green-50 border-green-200',
  },
  {
    id: 'documentation',
    icon: DocumentCheckIcon,
    title: 'Verify documentation',
    description: 'For valuable items, ask for receipts, warranties, or proof of ownership. Check serial numbers and authenticity when applicable.',
    color: 'text-purple-600 bg-purple-50 border-purple-200',
  },
];

export default function SafetyTips() {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div 
        className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <ShieldCheckIcon className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Safety Tips</h3>
              <p className="text-sm text-gray-600">Stay safe while buying and selling</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="w-5 h-5 text-orange-500" />
            {isExpanded ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            )}
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className="border-t border-gray-200">
          <div className="p-4 space-y-4">
            {safetyTips.map((tip) => {
              const IconComponent = tip.icon;
              return (
                <div
                  key={tip.id}
                  className={`p-4 rounded-lg border ${tip.color}`}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <IconComponent className="w-5 h-5" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">
                        {tip.title}
                      </h4>
                      <p className="text-sm text-gray-700">
                        {tip.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="px-4 pb-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <ExclamationTriangleIcon className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-gray-600">
                  <strong>Remember:</strong> If a deal seems too good to be true, it probably is. 
                  Trust your instincts and report suspicious activity to keep our marketplace safe for everyone.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
