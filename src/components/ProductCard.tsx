'use client';

import { useState } from 'react';
import Link from 'next/link';
import { DbProduct } from '@/types';
import { HeartIcon, LocationIcon } from '@/components/Icons';
import { useUser } from '@/context/UserContext';
import Message from './Message';
import VerificationBadge from './VerificationBadge';
import ReportButton from './ReportButton';
import LocationDisplay from './LocationDisplay';

interface ProductCardProps {
  product: DbProduct;
  showDistance?: boolean;
}

export default function ProductCard({ product, showDistance = false }: ProductCardProps) {
  const { addToWishlist, removeFromWishlist, isInWishlist } = useUser();
  const [isHovered, setIsHovered] = useState(false);

  const handleWishlistClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  const handleReport = (data: {
    itemId: string;
    itemType: string;
    reason: string;
    details: string;
  }) => {
    // Here we would typically send the report to a backend
    console.log('Report submitted:', data);
  };

  return (
    <div
      className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/products/${product.id}`}>
        <div className="relative">
          <img
            src="/placeholder.png"
            alt={product.name}
            className="w-full h-48 object-cover"
          />
          <button
            onClick={handleWishlistClick}
            className={`absolute top-2 right-2 p-2 rounded-full transition-colors ${
              isInWishlist(product.id)
                ? 'bg-red-500 text-white'
                : 'bg-white/80 text-slate-600 hover:bg-white'
            }`}
          >
            <HeartIcon className="w-5 h-5" />
          </button>
        </div>
        <div className="p-4">
          <h3 className="font-semibold text-lg mb-1">{product.name}</h3>
          <p className="text-slate-600 mb-2">
            {typeof product.price === 'number' ? `$${product.price.toFixed(2)}` : product.price}
          </p>
          {product.location && (
            <LocationDisplay
              location={product.location}
              showDistance={showDistance}
              className="mb-2"
            />
          )}
          {product.seller && (
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <span className="text-sm text-slate-600">{product.seller.name}</span>
                <VerificationBadge
                  status={product.seller.verificationStatus}
                  size="sm"
                />
              </div>
            </div>
          )}
        </div>
      </Link>
      <div className="px-4 pb-4 flex items-center justify-between">
        {product.seller && (
          <Message
            productId={product.id}
            sellerId={product.seller.id}
            productName={product.name}
            sellerName={product.seller.name || ''}
          />
        )}
        <ReportButton
          itemId={product.id}
          itemType="product"
          onReport={handleReport}
        />
      </div>
    </div>
  );
}