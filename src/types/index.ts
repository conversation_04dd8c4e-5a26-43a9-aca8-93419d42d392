import { Prisma } from '@prisma/client';

export type SessionUser = {
  id: string;
  name: string;
  email: string;
  image?: string;
};

export interface Chat {
  id: string;
  productId: string;
  buyerId: string;
  sellerId: string;
  status: 'ACTIVE' | 'ARCHIVED' | 'BLOCKED';
  createdAt: Date;
  updatedAt: Date;
  product: {
    id: string;
    title: string;
    images: string[];
  };
  buyer: {
    id: string;
    name: string;
    image: string;
  };
  seller: {
    id: string;
    name: string;
    image: string;
  };
  messages: Message[];
}

export interface Message {
  id: string;
  content: string;
  chatId: string;
  senderId: string;
  read: boolean;
  createdAt: Date;
  updatedAt: Date;
  attachments: MessageAttachment[];
}

export interface MessageAttachment {
  id: string;
  messageId: string;
  type: string;
  url: string;
  name: string;
  size: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface DbProduct {
  id: string;
  title: string;
  description: string;
  price: number;
  condition: string;
  location?: string;
  images: string[];
  categoryId: string;
  sellerId: string;
  status: 'ACTIVE' | 'SOLD' | 'ARCHIVED';
  createdAt: Date;
  updatedAt: Date;
  category: {
    id: string;
    name: string;
  };
  seller: {
    id: string;
    name: string;
    image: string;
    rating: number;
    verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  };
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
  count: number;
  description?: string;
}

export interface Seller {
  id: number;
  name: string;
  rating: number;
  activeListings: number;
  joined: string;
  location?: string;
  responseTime?: string;
  email?: string;
  phone?: string;
  verificationStatus?: 'Verified' | 'Unverified' | 'Pending' | 'Rejected';
  level: {
    current: number;
    title: string;
    points: number;
    nextLevelPoints: number;
  };
  performance: {
    totalSales: number;
    averageRating: number;
    responseRate: number;
    completionRate: number;
    disputeRate: number;
  };
  badges: {
    id: string;
    name: string;
    description: string;
    icon: string;
    earnedAt: string;
  }[];
  achievements: {
    id: string;
    name: string;
    description: string;
    progress: number;
    target: number;
    completed: boolean;
    reward: string;
  }[];
} 