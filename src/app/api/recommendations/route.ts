import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';

// GET /api/recommendations - Get personalized product recommendations
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam, 10) : 8;

    // If user is logged in, provide personalized recommendations
    if (session?.user) {
      const userId = session.user.id;

      // 1. Get user's recently viewed products (from database or localStorage)
      // 2. Get user's purchase history
      // 3. Get user's saved searches
      // 4. Use this data to generate personalized recommendations

      // Get user's recently viewed products (if stored in database)
      const recentlyViewed = await prisma.product.findMany({
        where: {
          views: {
            some: {
              userId: userId,
            },
          },
        },
        orderBy: {
          viewCount: 'desc',
        },
        take: 5,
        include: {
          category: true,
        },
      });

      // Get user's purchase history
      const purchaseHistory = await prisma.order.findMany({
        where: {
          buyerId: userId,
          status: {
            in: ['DELIVERED', 'SHIPPING'],
          },
        },
        include: {
          product: {
            include: {
              category: true,
            },
          },
        },
        take: 5,
      });

      // Get user's saved searches
      const savedSearches = await prisma.savedSearch.findMany({
        where: {
          userId: userId,
        },
        orderBy: {
          lastUsed: 'desc',
        },
        take: 3,
      });

      // Extract categories of interest
      const categoryIds = new Set<string>();

      // Add categories from recently viewed products
      recentlyViewed.forEach(product => {
        categoryIds.add(product.categoryId);
      });

      // Add categories from purchase history
      purchaseHistory.forEach(order => {
        categoryIds.add(order.product.categoryId);
      });

      // Add categories from saved searches
      savedSearches.forEach(search => {
        const filters = search.filters as any;
        if (filters.category && filters.category !== 'all') {
          // Find category by name or slug
          // This is a simplification - in a real app, you'd need to map the category name to ID
          categoryIds.add(filters.category);
        }
      });

      // Get recommendations based on user's interests
      let recommendations = await prisma.product.findMany({
        where: {
          OR: [
            // Products in categories of interest
            {
              categoryId: {
                in: Array.from(categoryIds),
              },
            },
            // Products from sellers the user has purchased from
            {
              sellerId: {
                in: purchaseHistory.map(order => order.product.sellerId),
              },
            },
          ],
          // Exclude products the user has already viewed or purchased
          AND: [
            {
              id: {
                notIn: [
                  ...recentlyViewed.map(product => product.id),
                  ...purchaseHistory.map(order => order.productId),
                ],
              },
            },
            {
              status: 'ACTIVE',
            },
          ],
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          seller: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });

      // If we don't have enough recommendations, add some popular products
      if (recommendations.length < limit) {
        const additionalCount = limit - recommendations.length;
        const existingIds = recommendations.map(product => product.id);

        const popularProducts = await prisma.product.findMany({
          where: {
            id: {
              notIn: existingIds,
            },
            status: 'ACTIVE',
          },
          include: {
            category: {
              select: {
                id: true,
                name: true,
              },
            },
            seller: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            viewCount: 'desc',
          },
          take: additionalCount,
        });

        recommendations = [...recommendations, ...popularProducts];
      }

      // Format the seller verification status to match the expected format in the frontend
      const formattedRecommendations = recommendations.map(product => ({
        ...product,
        seller: {
          ...product.seller,
          verificationStatus: 'PENDING' as const,
        },
      }));

      return NextResponse.json(formattedRecommendations);
    }
    // For non-logged in users, provide popular products
    else {
      const popularProducts = await prisma.product.findMany({
        where: {
          status: 'ACTIVE',
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          seller: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: [
          {
            viewCount: 'desc',
          },
          {
            createdAt: 'desc',
          },
        ],
        take: limit,
      });

      // Format the seller verification status to match the expected format in the frontend
      const formattedProducts = popularProducts.map(product => ({
        ...product,
        seller: {
          ...product.seller,
          verificationStatus: 'PENDING' as const,
        },
      }));

      return NextResponse.json(formattedProducts);
    }
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

// Helper function to map verification status from database enum to frontend format
function mapVerificationStatus(status: any): 'PENDING' | 'VERIFIED' | 'REJECTED' {
  switch (status) {
    case 'VERIFIED':
      return 'VERIFIED';
    case 'REJECTED':
      return 'REJECTED';
    case 'PENDING':
    default:
      return 'PENDING';
  }
}
