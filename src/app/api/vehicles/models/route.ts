import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const make = searchParams.get('make');
    const category = searchParams.get('category');

    if (!make || !category) {
      return NextResponse.json(
        { error: 'Make and category parameters are required' },
        { status: 400 }
      );
    }

    const models = await prisma.vehicleMakeModel.findMany({
      where: {
        make: make,
        category: category.toLowerCase(),
      },
      select: {
        model: true,
      },
      orderBy: {
        model: 'asc',
      },
    });

    return NextResponse.json(models.map(m => m.model));
  } catch (error) {
    console.error('Error fetching vehicle models:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 