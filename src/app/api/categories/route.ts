import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET endpoint to fetch all categories
export async function GET() {
  try {
    const categories = await prisma.category.findMany({
      include: {
        _count: {
          select: { products: true },
        },
        parent: {
          select: {
            id: true,
            name: true,
          },
        },
        subcategories: {
          select: {
            id: true,
            name: true,
            slug: true,
            icon: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json(
      categories.map((category) => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        icon: category.icon,
        description: category.description,
        productCount: category._count.products,
        parentId: category.parentId,
        parent: category.parent,
        subcategories: category.subcategories,
      }))
    );
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
