import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

interface RouteParams {
  params: { categoryId: string };
}

// GET endpoint to fetch attributes for a specific category
export async function GET(
  request: Request,
  { params }: RouteParams
) {
  try {
    const { categoryId } = params;

    // Check if category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { message: 'Category not found' },
        { status: 404 }
      );
    }

    // Fetch attributes for the category
    const attributes = await prisma.categoryAttribute.findMany({
      where: { categoryId },
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json(attributes);
  } catch (error) {
    console.error('Error fetching category attributes:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
