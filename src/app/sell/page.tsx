'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ProductPreview from '@/components/ProductPreview';
import { categories } from '@/lib/data';
import {
  PhotoIcon,
  XMarkIcon,
  EyeIcon,
  PaperAirplaneIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface FormData {
  name: string;
  description: string;
  price: string;
  categoryId: string;
  condition: string;
  images: File[];
  location: {
    address: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  attributes: Record<string, any>;
}

interface FormErrors {
  [key: string]: string;
}

export default function SellPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dbCategories, setDbCategories] = useState<any[]>([]);
  const [categoryAttributes, setCategoryAttributes] = useState<any[]>([]);

  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    price: '',
    categoryId: '',
    condition: 'new',
    images: [],
    location: {
      address: '',
      city: '',
      state: '',
      country: 'Nigeria',
      postalCode: '',
    },
    attributes: {},
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin?callbackUrl=/sell');
    }
  }, [session, status, router]);

  // Fetch categories from database
  useEffect(() => {
    fetchCategories();
  }, []);

  // Fetch category attributes when category changes
  useEffect(() => {
    if (formData.categoryId) {
      fetchCategoryAttributes(formData.categoryId);
    } else {
      setCategoryAttributes([]);
    }
  }, [formData.categoryId]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        setDbCategories(data);
      } else {
        // Fallback to mock categories
        setDbCategories(categories.map(cat => ({
          id: cat.id.toString(),
          name: cat.name,
          slug: cat.slug,
          icon: cat.icon,
        })));
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Fallback to mock categories
      setDbCategories(categories.map(cat => ({
        id: cat.id.toString(),
        name: cat.name,
        slug: cat.slug,
        icon: cat.icon,
      })));
    }
  };

  const fetchCategoryAttributes = async (categoryId: string) => {
    try {
      const response = await fetch(`/api/categories/${categoryId}/attributes`);
      if (response.ok) {
        const data = await response.json();
        setCategoryAttributes(data);
      }
    } catch (error) {
      console.error('Error fetching category attributes:', error);
      setCategoryAttributes([]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('location.')) {
      const locationField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        location: {
          ...prev.location,
          [locationField]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleAttributeChange = (attributeId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      attributes: {
        ...prev.attributes,
        [attributeId]: value,
      },
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => {
      const isValidType = file.type.startsWith('image/');
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB limit
      return isValidType && isValidSize;
    });

    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...validFiles].slice(0, 10), // Max 10 images
    }));
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    } else {
      const price = parseFloat(formData.price);
      if (isNaN(price) || price <= 0) {
        newErrors.price = 'Price must be a valid positive number';
      }
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'Category is required';
    }

    if (!formData.location.city.trim()) {
      newErrors['location.city'] = 'City is required';
    }

    if (!formData.location.state.trim()) {
      newErrors['location.state'] = 'State is required';
    }

    // Validate required category attributes
    categoryAttributes.forEach(attr => {
      if (attr.isRequired && !formData.attributes[attr.id]) {
        newErrors[`attribute_${attr.id}`] = `${attr.name} is required`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePreview = () => {
    if (validateForm()) {
      setShowPreview(true);
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // In a real app, you'd upload images first and get URLs
      const imageUrls = formData.images.map(file => URL.createObjectURL(file));

      const productData = {
        name: formData.name,
        description: formData.description,
        price: formData.price,
        categoryId: formData.categoryId,
        condition: formData.condition,
        images: imageUrls,
        location: formData.location,
        attributes: formData.attributes,
      };

      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (response.ok) {
        const product = await response.json();
        router.push(`/products/${product.id}?success=true`);
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create product');
      }
    } catch (error) {
      console.error('Error creating product:', error);
      alert(error instanceof Error ? error.message : 'Failed to create product');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect
  }

  const selectedCategory = dbCategories.find(cat => cat.id === formData.categoryId);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <Header
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
      />

      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Sell Your Product</h1>
            <p className="text-gray-600">Create a listing to reach thousands of potential buyers</p>
          </div>

          {/* Form */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <form className="space-y-6">
              {/* Basic Information */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Product Name */}
                  <div className="md:col-span-2">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Product Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                        errors.name ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter a descriptive title for your product"
                    />
                    {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
                  </div>

                  {/* Price */}
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                      Price (₦) <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      id="price"
                      name="price"
                      value={formData.price}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                        errors.price ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="0"
                      min="0"
                      step="0.01"
                    />
                    {errors.price && <p className="mt-1 text-sm text-red-500">{errors.price}</p>}
                  </div>

                  {/* Category */}
                  <div>
                    <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 mb-2">
                      Category <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="categoryId"
                      name="categoryId"
                      value={formData.categoryId}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                        errors.categoryId ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select a category</option>
                      {dbCategories.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.icon} {category.name}
                        </option>
                      ))}
                    </select>
                    {errors.categoryId && <p className="mt-1 text-sm text-red-500">{errors.categoryId}</p>}
                  </div>

                  {/* Condition */}
                  <div className="md:col-span-2">
                    <label htmlFor="condition" className="block text-sm font-medium text-gray-700 mb-2">
                      Condition
                    </label>
                    <select
                      id="condition"
                      name="condition"
                      value={formData.condition}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    >
                      <option value="new">New</option>
                      <option value="like-new">Like New</option>
                      <option value="good">Good</option>
                      <option value="fair">Fair</option>
                      <option value="poor">Poor</option>
                    </select>
                  </div>

                  {/* Description */}
                  <div className="md:col-span-2">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                      placeholder="Describe your product in detail..."
                    />
                  </div>
                </div>
              </div>

              {/* Category-specific Attributes */}
              {categoryAttributes.length > 0 && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Product Details</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {categoryAttributes.map(attribute => (
                      <div key={attribute.id}>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {attribute.name} {attribute.isRequired && <span className="text-red-500">*</span>}
                        </label>
                        {attribute.type === 'select' ? (
                          <select
                            value={formData.attributes[attribute.id] || ''}
                            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                              errors[`attribute_${attribute.id}`] ? 'border-red-500' : 'border-gray-300'
                            }`}
                          >
                            <option value="">Select {attribute.name}</option>
                            {attribute.options.map((option: string) => (
                              <option key={option} value={option}>{option}</option>
                            ))}
                          </select>
                        ) : attribute.type === 'number' ? (
                          <input
                            type="number"
                            value={formData.attributes[attribute.id] || ''}
                            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                              errors[`attribute_${attribute.id}`] ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder={`Enter ${attribute.name.toLowerCase()}`}
                          />
                        ) : attribute.type === 'boolean' ? (
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              checked={formData.attributes[attribute.id] || false}
                              onChange={(e) => handleAttributeChange(attribute.id, e.target.checked)}
                              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-600">Yes</span>
                          </div>
                        ) : (
                          <input
                            type="text"
                            value={formData.attributes[attribute.id] || ''}
                            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                              errors[`attribute_${attribute.id}`] ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder={`Enter ${attribute.name.toLowerCase()}`}
                          />
                        )}
                        {errors[`attribute_${attribute.id}`] && (
                          <p className="mt-1 text-sm text-red-500">{errors[`attribute_${attribute.id}`]}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Images */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Photos</h2>
                <div className="space-y-4">
                  {/* Upload Area */}
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer">
                      <PhotoIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-lg font-medium text-gray-900 mb-2">Upload Photos</p>
                      <p className="text-sm text-gray-500">
                        Add up to 10 photos. First photo will be the cover image.
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        Supported formats: JPG, PNG, GIF (Max 5MB each)
                      </p>
                    </label>
                  </div>

                  {/* Image Preview */}
                  {formData.images.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {formData.images.map((image, index) => (
                        <div key={index} className="relative group">
                          <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                            <img
                              src={URL.createObjectURL(image)}
                              alt={`Upload ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <XMarkIcon className="w-4 h-4" />
                          </button>
                          {index === 0 && (
                            <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                              Cover
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Location */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Location</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="location.city" className="block text-sm font-medium text-gray-700 mb-2">
                      City <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="location.city"
                      name="location.city"
                      value={formData.location.city}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                        errors['location.city'] ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="e.g., Lagos"
                    />
                    {errors['location.city'] && <p className="mt-1 text-sm text-red-500">{errors['location.city']}</p>}
                  </div>

                  <div>
                    <label htmlFor="location.state" className="block text-sm font-medium text-gray-700 mb-2">
                      State <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="location.state"
                      name="location.state"
                      value={formData.location.state}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                        errors['location.state'] ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="e.g., Lagos State"
                    />
                    {errors['location.state'] && <p className="mt-1 text-sm text-red-500">{errors['location.state']}</p>}
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="location.address" className="block text-sm font-medium text-gray-700 mb-2">
                      Address (Optional)
                    </label>
                    <input
                      type="text"
                      id="location.address"
                      name="location.address"
                      value={formData.location.address}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder="Street address or area"
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={handlePreview}
                  className="flex items-center justify-center space-x-2 px-6 py-3 border border-indigo-600 text-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors font-medium"
                >
                  <EyeIcon className="w-5 h-5" />
                  <span>Preview</span>
                </button>
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="flex items-center justify-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium flex-1"
                >
                  <PaperAirplaneIcon className="w-5 h-5" />
                  <span>{isSubmitting ? 'Publishing...' : 'Publish Listing'}</span>
                </button>
              </div>

              {/* Warning */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium mb-1">Before you publish:</p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>Make sure all information is accurate and complete</li>
                      <li>Use clear, high-quality photos</li>
                      <li>Set a fair and competitive price</li>
                      <li>Your listing will be reviewed before going live</li>
                    </ul>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </main>

      <Footer categories={categories} />

      {/* Preview Modal */}
      {showPreview && (
        <ProductPreview
          productData={{
            ...formData,
            category: selectedCategory?.name || '',
          }}
          onClose={() => setShowPreview(false)}
          onEdit={() => setShowPreview(false)}
        />
      )}
    </div>
  );
}
