import React from 'react';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import Image from 'next/image';
import StartChatButton from '@/components/StartChatButton';
import FollowButtonClient from '@/components/FollowButtonClient';
import ShareButtonClient from '@/components/ShareButtonClient';
import ProductReviews from '@/components/ProductReviews';
import SafetyTips from '@/components/SafetyTips';

async function getProduct(id: string) {
  return prisma.product.findUnique({
    where: { id },
    include: {
      seller: {
        select: {
          id: true,
          name: true,
          image: true,
          rating: true,
          verificationStatus: true,
        },
      },
      category: true,
    },
  });
}

export default async function ProductPage({
  params,
}: {
  params: { id: string };
}) {
  const session = await getServerSession(authOptions);
  const product = await getProduct(params.id);

  if (!product) {
    return <div>Product not found</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-square">
              <Image
                src={product.images?.[0] || '/placeholder-product.jpg'}
                alt={product.name}
                fill
                className="object-cover rounded-lg"
              />
            </div>
            <div className="grid grid-cols-4 gap-4">
              {product.images?.slice(1).map((image, index) => (
                <div key={index} className="relative aspect-square">
                  <Image
                    src={image}
                    alt={`${product.name} - Image ${index + 2}`}
                    fill
                    className="object-cover rounded-lg"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">{product.name}</h1>
              <p className="text-2xl font-semibold text-indigo-600 mt-2">
                ${product.price.toFixed(2)}
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative w-12 h-12">
                  <Image
                    src={product.seller.image || '/default-avatar.png'}
                    alt={product.seller.name}
                    fill
                    className="rounded-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-medium">{product.seller.name}</h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">
                      Rating: {product.seller.rating.toFixed(1)}
                    </span>
                    {product.seller.verificationStatus === 'VERIFIED' && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                        Verified
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <FollowButtonClient sellerId={product.seller.id} />
            </div>

            <div className="prose max-w-none">
              <p>{product.description}</p>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Category</h3>
                <p className="text-gray-600">{product.category.name}</p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Condition</h3>
                <p className="text-gray-600">{product.condition}</p>
              </div>

              {product.location && (
                <div>
                  <h3 className="font-medium mb-2">Location</h3>
                  <p className="text-gray-600">{product.location}</p>
                </div>
              )}
            </div>

            <div className="pt-6 space-y-4">
              <div className="flex items-center space-x-4">
                <StartChatButton
                  productId={product.id}
                  sellerId={product.seller.id}
                  className="flex-1"
                />
                <ShareButtonClient
                  title={product.name}
                  description={product.description || undefined}
                />
              </div>
              <div className="text-sm text-gray-500 flex items-center justify-end">
                <span>{product.shareCount || 0} shares</span>
              </div>
            </div>
          </div>
        </div>

        {/* Safety Tips Section */}
        <div className="mt-8">
          <SafetyTips />
        </div>

        {/* Reviews Section */}
        <div className="mt-8">
          <ProductReviews
            productId={product.id}
            sellerId={product.seller.id}
          />
        </div>
      </div>
    </div>
  );
}