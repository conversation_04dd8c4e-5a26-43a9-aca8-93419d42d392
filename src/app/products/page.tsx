'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ProductCard from '@/components/ProductCard';
import FilterSidebar from '@/components/FilterSidebar';
import { products, categories } from '@/lib/data';
import type { Product } from '@/types';
import { HiViewGrid, HiViewList, HiFilter } from 'react-icons/hi';

export default function ProductsPage() {
  const [cart, setCart] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isMobile, setIsMobile] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: 'all',
    condition: 'all',
    location: 'all',
    priceRange: [0, 1000000] as [number, number],
    sortBy: 'newest',
    attributes: {},
  });

  // Check if we're on mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const parsePrice = (price: string) => {
    return parseInt(price.replace(/[^0-9]/g, ''));
  };

  const filteredProducts = products
    .filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = filters.category === 'all' || product.category === filters.category;
      const matchesCondition = filters.condition === 'all' || product.condition === filters.condition;
      const matchesLocation = filters.location === 'all' || product.location === filters.location;
      const productPrice = parsePrice(product.price);
      const matchesPrice = productPrice >= filters.priceRange[0] && productPrice <= filters.priceRange[1];

      return matchesSearch && matchesCategory && matchesCondition && matchesLocation && matchesPrice;
    })
    .sort((a, b) => {
      const priceA = parsePrice(a.price);
      const priceB = parsePrice(b.price);

      switch (filters.sortBy) {
        case 'price_low':
          return priceA - priceB;
        case 'price_high':
          return priceB - priceA;
        case 'oldest':
          return Number(a.id) - Number(b.id);
        case 'popular':
          return Number(b.id) - Number(a.id);
        case 'newest':
        default:
          return Number(b.id) - Number(a.id);
      }
    });

  // Define filter options
  const conditionOptions = [
    { id: 'all', name: 'All Conditions' },
    { id: 'new', name: 'New' },
    { id: 'used', name: 'Used' },
    { id: 'refurbished', name: 'Refurbished' },
  ];

  const locationOptions = [
    { id: 'all', name: 'All Locations' },
    { id: 'lagos', name: 'Lagos' },
    { id: 'abuja', name: 'Abuja' },
    { id: 'port-harcourt', name: 'Port Harcourt' },
  ];

  const categoryOptions = [
    { id: 'all', name: 'All Categories' },
    ...categories.map(cat => ({ id: cat.slug, name: cat.name })),
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <Header 
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        cart={cart}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
      />
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">All Products</h1>
          <p className="text-slate-600">{products.length} items available</p>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="flex items-center gap-4">
            {isMobile && (
              <button
                onClick={() => setShowMobileFilters(!showMobileFilters)}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition"
              >
                <HiFilter className="h-5 w-5 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Filters</span>
              </button>
            )}

            <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${
                  viewMode === 'grid' ? 'bg-indigo-100 text-indigo-600' : 'bg-white text-gray-500'
                }`}
              >
                <HiViewGrid className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${
                  viewMode === 'list' ? 'bg-indigo-100 text-indigo-600' : 'bg-white text-gray-500'
                }`}
              >
                <HiViewList className="h-5 w-5" />
              </button>
            </div>
          </div>

          <p className="text-sm text-gray-500">
            Showing {filteredProducts.length} of {products.length} products
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar for desktop or mobile when expanded */}
          <div className={`${isMobile && !showMobileFilters ? 'hidden' : 'block'} md:block md:w-64 flex-shrink-0`}>
            <FilterSidebar
              categories={categoryOptions}
              conditions={conditionOptions}
              locations={locationOptions}
              priceRange={{ min: 0, max: 1000000 }}
              onFilterChange={(newFilters) => setFilters({ ...filters, ...newFilters })}
              isMobile={isMobile}
            />
          </div>

          {/* Main content */}
          <div className="flex-1">
            {/* Browse by Category */}
            <section className="mb-12">
              <h2 className="text-xl font-semibold mb-4">Browse by Category</h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {categories.map((category) => (
                  <a
                    key={category.id}
                    href={`/categories/${category.slug}`}
                    className="group p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-slate-100 text-center"
                  >
                    <div className="text-3xl mb-2 group-hover:scale-110 transition-transform">{category.icon}</div>
                    <h3 className="font-medium text-sm">{category.name}</h3>
                    <p className="text-xs text-slate-500 mt-1">{category.count} items</p>
                  </a>
                ))}
              </div>
            </section>

            {/* Product Grid/List */}
            {filteredProducts.length > 0 ? (
              viewMode === 'grid' ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredProducts.map((product) => (
                    <ProductCard
                      key={product.id}
                      product={product}
                    />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredProducts.map((product) => (
                    <div key={product.id} className="flex flex-col md:flex-row bg-white rounded-lg shadow-sm overflow-hidden">
                      <div className="md:w-1/4 h-48 md:h-auto">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1 p-6">
                        <h3 className="text-lg font-semibold mb-2">{product.name}</h3>
                        <p className="text-gray-600 mb-4">{product.price}</p>
                        <div className="flex justify-between items-end">
                          <button
                            onClick={() => setCart([...cart, product])}
                            className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition"
                          >
                            Add to Cart
                          </button>
                          <span className="text-sm text-gray-500">
                            {product.condition && `Condition: ${product.condition}`}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )
            ) : (
              <div className="text-center py-12">
                <p className="text-xl text-slate-600">No products found matching your criteria</p>
              </div>
            )}
          </div>
        </div>
      </main>
      <Footer categories={categories} />
    </div>
  );
} 