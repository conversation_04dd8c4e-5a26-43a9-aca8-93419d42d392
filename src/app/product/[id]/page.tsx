'use client';

import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { products, categories } from '@/lib/data';
import { StarIcon, HeartIcon, ChatIcon, LocationIcon, ChevronLeftIcon, ChevronRightIcon } from '@/components/Icons';
import { useUser } from '@/context/UserContext';
import RecentlyViewed from '@/components/RecentlyViewed';
import ProductCard from '@/components/ProductCard'; // Added import
import SafetyTips from '@/components/SafetyTips';
import { Product } from '@/types';

export default function ProductDetail() {
  const { id } = useParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [cart, setCart] = useState<Product[]>([]);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const { addToRecentlyViewed, addToWishlist, removeFromWishlist, isInWishlist } = useUser();

  const product = products.find(p => p.id === Number(id));

  // Mock product images array - in a real app, this would come from the product data
  const productImages = [
    product?.image,
    'https://placehold.co/800x600/3b82f6/ffffff?text=Product+Image+2',
    'https://placehold.co/800x600/10b981/ffffff?text=Product+Image+3',
    'https://placehold.co/800x600/8b5cf6/ffffff?text=Product+Image+4',
  ].filter(Boolean) as string[];

  useEffect(() => {
    if (product) {
      // Load existing recently viewed products
      const stored = localStorage.getItem('recentlyViewed');
      let recentProducts: Product[] = [];

      if (stored) {
        try {
          recentProducts = JSON.parse(stored);
        } catch (error) {
          console.error('Error parsing recently viewed products:', error);
        }
      }

      // Remove the current product if it exists
      recentProducts = recentProducts.filter(p => p.id !== product.id);

      // Add the current product to the beginning
      recentProducts.unshift(product);

      // Keep only the most recent 10 products
      recentProducts = recentProducts.slice(0, 10);

      // Save back to localStorage
      localStorage.setItem('recentlyViewed', JSON.stringify(recentProducts));
    }
  }, [product]);

  const handlePreviousImage = () => {
    setSelectedImageIndex((prev) => (prev === 0 ? productImages.length - 1 : prev - 1));
  };

  const handleNextImage = () => {
    setSelectedImageIndex((prev) => (prev === productImages.length - 1 ? 0 : prev + 1));
  };

  const findSimilarItems = (currentProduct: Product) => {
    if (!currentProduct) return [];
    return products
      .filter(p =>
        p.id !== currentProduct.id &&
        (p.category === currentProduct.category || p.condition === currentProduct.condition || p.name.split(' ')[0] === currentProduct.name.split(' ')[0])
      )
      .slice(0, 4); // Show up to 4 similar items
  };

  if (!product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        <Header
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          isMenuOpen={isMenuOpen}
          setIsMenuOpen={setIsMenuOpen}
        />
        <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-2xl font-bold text-center">Product not found</h1>
        </main>
        <Footer categories={categories} />
      </div>
    );
  }

  const handleWishlistClick = () => {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  const getAvailabilityColor = (status?: string) => {
    switch (status) {
      case 'Available':
        return 'bg-green-100 text-green-700';
      case 'Sold':
        return 'bg-red-100 text-red-700';
      case 'Under Contract':
        return 'bg-yellow-100 text-yellow-700';
      default:
        return 'bg-slate-100 text-slate-700';
    }
  };

  const getConditionColor = (condition?: string) => {
    switch (condition?.toLowerCase()) {
      case 'new':
        return 'bg-green-100 text-green-700';
      case 'like new':
        return 'bg-emerald-100 text-emerald-700';
      case 'good':
        return 'bg-blue-100 text-blue-700';
      case 'fair':
        return 'bg-yellow-100 text-yellow-700';
      case 'poor':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-slate-100 text-slate-700';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <Header
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
      />
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {/* Image Gallery */}
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="relative">
                <img
                  src={productImages[selectedImageIndex]}
                  alt={product.name}
                  className="w-full h-96 object-cover"
                />
                {productImages.length > 1 && (
                  <>
                    <button
                      onClick={handlePreviousImage}
                      className="absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/80 hover:bg-white text-slate-600 transition-colors"
                    >
                      <ChevronLeftIcon className="w-6 h-6" />
                    </button>
                    <button
                      onClick={handleNextImage}
                      className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/80 hover:bg-white text-slate-600 transition-colors"
                    >
                      <ChevronRightIcon className="w-6 h-6" />
                    </button>
                  </>
                )}
              </div>
              <div className="p-4 flex gap-2 overflow-x-auto">
                {productImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                      selectedImageIndex === index
                        ? 'border-indigo-600'
                        : 'border-transparent hover:border-slate-200'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.name} - Image ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* Product Info */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex justify-between items-start mb-4">
                <h1 className="text-3xl font-bold">{product.name}</h1>
                <button
                  onClick={handleWishlistClick}
                  className={`p-2 rounded-full transition-colors ${
                    isInWishlist(product.id)
                      ? 'bg-red-500 text-white'
                      : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
                  }`}
                >
                  <HeartIcon className="w-6 h-6" />
                </button>
              </div>
              <p className="text-2xl font-bold text-indigo-600 mb-4">{product.price}</p>

              <div className="flex flex-wrap gap-2 mb-6">
                {product.availability && (
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${getAvailabilityColor(product.availability)}`}>
                    {product.availability}
                  </div>
                )}
                {product.condition && (
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${getConditionColor(product.condition)}`}>
                    {product.condition}
                  </div>
                )}
              </div>

              <div className="space-y-4 mb-6">
                {product.category && (
                  <div className="flex items-center text-slate-600">
                    <span className="font-medium w-24">Category:</span>
                    <span>{product.category}</span>
                  </div>
                )}
                {product.location && (
                  <div className="flex items-center text-slate-600">
                    <LocationIcon className="w-5 h-5 mr-2" />
                    <span>{product.location}</span>
                  </div>
                )}
              </div>

              {product.description && (
                <div className="mb-6">
                  <h2 className="text-xl font-semibold mb-2">Description</h2>
                  <div className="prose prose-slate max-w-none">
                    <p className="text-slate-600 whitespace-pre-line">{product.description}</p>
                  </div>
                </div>
              )}

              {/* Product Details */}
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-2">Product Details</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-slate-50 p-3 rounded-lg">
                    <p className="text-sm text-slate-500">Brand</p>
                    <p className="font-medium">{product.brand || 'Not specified'}</p>
                  </div>
                  <div className="bg-slate-50 p-3 rounded-lg">
                    <p className="text-sm text-slate-500">Model</p>
                    <p className="font-medium">{product.model || 'Not specified'}</p>
                  </div>
                  <div className="bg-slate-50 p-3 rounded-lg">
                    <p className="text-sm text-slate-500">Year</p>
                    <p className="font-medium">{product.year || 'Not specified'}</p>
                  </div>
                  <div className="bg-slate-50 p-3 rounded-lg">
                    <p className="text-sm text-slate-500">Warranty</p>
                    <p className="font-medium">{product.warranty || 'Not specified'}</p>
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                <button className="flex-1 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition">
                  Contact Seller
                </button>
                <button className="flex items-center justify-center gap-2 px-6 py-3 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 transition">
                  <ChatIcon className="w-5 h-5" />
                  <span>Message</span>
                </button>
              </div>
            </div>
          </div>

          {/* Safety Tips Section */}
          <section className="mb-12">
            <SafetyTips />
          </section>

          {/* Reviews Section */}
          <section className="mb-12">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-2xl font-bold mb-4">Reviews & Feedback</h2>
              <div className="text-center py-8 text-gray-500">
                <p>Reviews are available for products in the database.</p>
                <p className="text-sm mt-2">This is a demo product using mock data.</p>
              </div>
            </div>
          </section>

          {/* Similar Items Section */}
          {product && findSimilarItems(product).length > 0 && (
            <section className="mb-12">
              <h2 className="text-2xl font-bold mb-6">Similar Items</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {findSimilarItems(product).map((similarProduct) => (
                  <ProductCard key={similarProduct.id} product={similarProduct} />
                ))}
              </div>
            </section>
          )}

          <RecentlyViewed />
        </div>
      </main>
      <Footer categories={categories} />
    </div>
  );
}